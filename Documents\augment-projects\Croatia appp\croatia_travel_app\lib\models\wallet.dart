/// 💳 WALLET MODELS - Modely pro peněženku a platby

/// <PERSON>lavní peněženka
class Wallet {
  final String id;
  final String ownerName;
  final double balance;
  final String currency;
  final DateTime createdAt;
  final bool isActive;
  final List<VirtualCard> virtualCards;
  final CashbackSettings? cashbackSettings;

  const Wallet({
    required this.id,
    required this.ownerName,
    required this.balance,
    required this.currency,
    required this.createdAt,
    required this.isActive,
    this.virtualCards = const [],
    this.cashbackSettings,
  });

  Wallet copyWith({
    String? id,
    String? ownerName,
    double? balance,
    String? currency,
    DateTime? createdAt,
    bool? isActive,
    List<VirtualCard>? virtualCards,
    CashbackSettings? cashbackSettings,
  }) {
    return Wallet(
      id: id ?? this.id,
      ownerName: ownerName ?? this.ownerName,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      virtualCards: virtualCards ?? this.virtualCards,
      cashbackSettings: cashbackSettings ?? this.cashbackSettings,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ownerName': ownerName,
      'balance': balance,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
      'virtualCards': virtualCards.map((c) => c.toJson()).toList(),
      'cashbackSettings': cashbackSettings?.toJson(),
    };
  }

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      id: json['id'] as String,
      ownerName: json['ownerName'] as String,
      balance: (json['balance'] as num).toDouble(),
      currency: json['currency'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isActive: json['isActive'] as bool,
      virtualCards: (json['virtualCards'] as List<dynamic>?)
          ?.map((c) => VirtualCard.fromJson(c as Map<String, dynamic>))
          .toList() ?? [],
      cashbackSettings: json['cashbackSettings'] != null
          ? CashbackSettings.fromJson(json['cashbackSettings'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// Virtuální karta
class VirtualCard {
  final String id;
  final String walletId;
  final String cardNumber;
  final DateTime expiryDate;
  final String cvv;
  final String cardholderName;
  final bool isActive;
  final DateTime createdAt;

  const VirtualCard({
    required this.id,
    required this.walletId,
    required this.cardNumber,
    required this.expiryDate,
    required this.cvv,
    required this.cardholderName,
    required this.isActive,
    required this.createdAt,
  });

  String get maskedCardNumber {
    if (cardNumber.length < 4) return cardNumber;
    return '**** **** **** ${cardNumber.substring(cardNumber.length - 4)}';
  }

  bool get isExpired => DateTime.now().isAfter(expiryDate);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'walletId': walletId,
      'cardNumber': cardNumber,
      'expiryDate': expiryDate.toIso8601String(),
      'cvv': cvv,
      'cardholderName': cardholderName,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory VirtualCard.fromJson(Map<String, dynamic> json) {
    return VirtualCard(
      id: json['id'] as String,
      walletId: json['walletId'] as String,
      cardNumber: json['cardNumber'] as String,
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      cvv: json['cvv'] as String,
      cardholderName: json['cardholderName'] as String,
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }
}

/// Transakce
class Transaction {
  final String id;
  final TransactionType type;
  final double amount;
  final String currency;
  final String description;
  final DateTime timestamp;
  final TransactionStatus status;
  final PaymentMethod? paymentMethod;
  final String? merchantId;
  final String? merchantName;

  const Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.currency,
    required this.description,
    required this.timestamp,
    required this.status,
    this.paymentMethod,
    this.merchantId,
    this.merchantName,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'amount': amount,
      'currency': currency,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'paymentMethod': paymentMethod?.name,
      'merchantId': merchantId,
      'merchantName': merchantName,
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      type: TransactionType.values.firstWhere((e) => e.name == json['type']),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: TransactionStatus.values.firstWhere((e) => e.name == json['status']),
      paymentMethod: json['paymentMethod'] != null
          ? PaymentMethod.values.firstWhere((e) => e.name == json['paymentMethod'])
          : null,
      merchantId: json['merchantId'] as String?,
      merchantName: json['merchantName'] as String?,
    );
  }
}

/// Typ transakce
enum TransactionType {
  deposit,    // Dobití
  payment,    // Platba
  refund,     // Vrácení
  transfer,   // Převod
  loyalty,    // Loyalty body
  reward,     // Výměna odměny
  cashback,   // Cashback
}

/// Stav transakce
enum TransactionStatus {
  pending,    // Čekající
  completed,  // Dokončená
  failed,     // Neúspěšná
  cancelled,  // Zrušená
}

/// Způsob platby
enum PaymentMethod {
  card,       // Karta
  bankTransfer, // Bankovní převod
  paypal,     // PayPal
  googlePay,  // Google Pay
  applePay,   // Apple Pay
  crypto,     // Kryptoměny
}

/// Loyalty karta
class LoyaltyCard {
  final String id;
  final String name;
  final LoyaltyCardType type;
  final int points;
  final LoyaltyTier tier;
  final DateTime issuedAt;
  final DateTime? expiresAt;

  const LoyaltyCard({
    required this.id,
    required this.name,
    required this.type,
    required this.points,
    required this.tier,
    required this.issuedAt,
    this.expiresAt,
  });

  LoyaltyCard copyWith({
    String? id,
    String? name,
    LoyaltyCardType? type,
    int? points,
    LoyaltyTier? tier,
    DateTime? issuedAt,
    DateTime? expiresAt,
  }) {
    return LoyaltyCard(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      points: points ?? this.points,
      tier: tier ?? this.tier,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'points': points,
      'tier': tier.name,
      'issuedAt': issuedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
    };
  }

  factory LoyaltyCard.fromJson(Map<String, dynamic> json) {
    return LoyaltyCard(
      id: json['id'] as String,
      name: json['name'] as String,
      type: LoyaltyCardType.values.firstWhere((e) => e.name == json['type']),
      points: json['points'] as int,
      tier: LoyaltyTier.values.firstWhere((e) => e.name == json['tier']),
      issuedAt: DateTime.parse(json['issuedAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
    );
  }
}

/// Typ loyalty karty
enum LoyaltyCardType {
  main,       // Hlavní karta
  partner,    // Partnerská karta
  seasonal,   // Sezónní karta
}

/// Úroveň loyalty
enum LoyaltyTier {
  bronze,
  silver,
  gold,
  platinum,
}

/// Odměna
class Reward {
  final String id;
  final String title;
  final String description;
  final int pointsCost;
  final String category;
  final DateTime validUntil;
  final String? imageUrl;

  const Reward({
    required this.id,
    required this.title,
    required this.description,
    required this.pointsCost,
    required this.category,
    required this.validUntil,
    this.imageUrl,
  });

  bool get isExpired => DateTime.now().isAfter(validUntil);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'pointsCost': pointsCost,
      'category': category,
      'validUntil': validUntil.toIso8601String(),
      'imageUrl': imageUrl,
    };
  }

  factory Reward.fromJson(Map<String, dynamic> json) {
    return Reward(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      pointsCost: json['pointsCost'] as int,
      category: json['category'] as String,
      validUntil: DateTime.parse(json['validUntil'] as String),
      imageUrl: json['imageUrl'] as String?,
    );
  }
}

/// Nastavení cashback
class CashbackSettings {
  final double defaultRate; // %
  final Map<String, double> categoryRates; // kategorie -> %
  final double maxCashbackPerMonth;
  final bool isEnabled;

  const CashbackSettings({
    required this.defaultRate,
    required this.categoryRates,
    required this.maxCashbackPerMonth,
    required this.isEnabled,
  });

  Map<String, dynamic> toJson() {
    return {
      'defaultRate': defaultRate,
      'categoryRates': categoryRates,
      'maxCashbackPerMonth': maxCashbackPerMonth,
      'isEnabled': isEnabled,
    };
  }

  factory CashbackSettings.fromJson(Map<String, dynamic> json) {
    return CashbackSettings(
      defaultRate: (json['defaultRate'] as num).toDouble(),
      categoryRates: Map<String, double>.from(
        (json['categoryRates'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      maxCashbackPerMonth: (json['maxCashbackPerMonth'] as num).toDouble(),
      isEnabled: json['isEnabled'] as bool,
    );
  }
}
