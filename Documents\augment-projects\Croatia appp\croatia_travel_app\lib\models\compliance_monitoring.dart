/// 🔒 COMPLIANCE MONITORING MODELS - Modely pro monitoring compliance
library compliance_monitoring;

import 'package:json_annotation/json_annotation.dart';

part 'compliance_monitoring.g.dart';

/// Compliance framework enum
enum ComplianceFramework {
  gdpr,
  soc2,
  iso27001,
  pci,
  hipaa,
  custom,
}

/// Rule severity enum
enum RuleSeverity {
  low,
  medium,
  high,
  critical,
}

/// Compliance status enum
enum ComplianceStatus {
  compliant,
  nonCompliant,
  pending,
  unknown,
  inProgress,
}

/// Framework status enum
enum FrameworkStatus {
  compliant,
  partiallyCompliant,
  nonCompliant,
  notApplicable,
}

/// Compliance rule model
@JsonSerializable()
class ComplianceRule {
  final String id;
  final String name;
  final String description;
  final ComplianceFramework framework;
  final RuleSeverity severity;
  final Duration checkInterval;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastChecked;
  final Map<String, dynamic>? metadata;

  const ComplianceRule({
    required this.id,
    required this.name,
    required this.description,
    required this.framework,
    required this.severity,
    required this.checkInterval,
    this.isActive = true,
    required this.createdAt,
    this.lastChecked,
    this.metadata,
  });

  factory ComplianceRule.fromJson(Map<String, dynamic> json) =>
      _$ComplianceRuleFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceRuleToJson(this);
}

/// Compliance check result model
@JsonSerializable()
class ComplianceCheckResult {
  final String ruleId;
  final bool isCompliant;
  final String? message;
  final Map<String, dynamic>? details;
  final DateTime checkedAt;
  final String? evidence;
  final List<String>? recommendations;

  const ComplianceCheckResult({
    required this.ruleId,
    required this.isCompliant,
    this.message,
    this.details,
    required this.checkedAt,
    this.evidence,
    this.recommendations,
  });

  factory ComplianceCheckResult.fromJson(Map<String, dynamic> json) =>
      _$ComplianceCheckResultFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceCheckResultToJson(this);
}

/// Compliance violation model
@JsonSerializable()
class ComplianceViolation {
  final String id;
  final String ruleId;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime detectedAt;
  final DateTime? resolvedAt;
  final String? resolution;
  final Map<String, dynamic>? context;
  final bool isResolved;

  const ComplianceViolation({
    required this.id,
    required this.ruleId,
    required this.title,
    required this.description,
    required this.severity,
    required this.detectedAt,
    this.resolvedAt,
    this.resolution,
    this.context,
    this.isResolved = false,
  });

  factory ComplianceViolation.fromJson(Map<String, dynamic> json) =>
      _$ComplianceViolationFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceViolationToJson(this);
}

/// Compliance event model
@JsonSerializable()
class ComplianceEvent {
  final String id;
  final String type;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final String? ruleId;
  final String? violationId;

  const ComplianceEvent({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.severity,
    required this.timestamp,
    this.data,
    this.ruleId,
    this.violationId,
  });

  factory ComplianceEvent.fromJson(Map<String, dynamic> json) =>
      _$ComplianceEventFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceEventToJson(this);
}

/// Compliance report model
@JsonSerializable()
class ComplianceReport {
  final String id;
  final DateTime generatedAt;
  final DateTime periodStart;
  final DateTime periodEnd;
  final int totalRules;
  final int compliantRules;
  final int nonCompliantRules;
  final int pendingRules;
  final double complianceScore;
  final List<ComplianceCheckResult> results;
  final List<ComplianceViolation> violations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final Map<String, dynamic>? summary;

  const ComplianceReport({
    required this.id,
    required this.generatedAt,
    required this.periodStart,
    required this.periodEnd,
    required this.totalRules,
    required this.compliantRules,
    required this.nonCompliantRules,
    required this.pendingRules,
    required this.complianceScore,
    required this.results,
    required this.violations,
    required this.frameworkStatus,
    this.summary,
  });

  factory ComplianceReport.fromJson(Map<String, dynamic> json) =>
      _$ComplianceReportFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceReportToJson(this);
}

/// Compliance dashboard model
@JsonSerializable()
class ComplianceDashboard {
  final DateTime lastUpdated;
  final double overallScore;
  final int totalViolations;
  final int criticalViolations;
  final int resolvedViolations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final List<ComplianceEvent> recentEvents;
  final Map<String, dynamic> trends;
  final Map<String, int> violationsByCategory;

  const ComplianceDashboard({
    required this.lastUpdated,
    required this.overallScore,
    required this.totalViolations,
    required this.criticalViolations,
    required this.resolvedViolations,
    required this.frameworkStatus,
    required this.recentEvents,
    required this.trends,
    required this.violationsByCategory,
  });

  factory ComplianceDashboard.fromJson(Map<String, dynamic> json) =>
      _$ComplianceDashboardFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceDashboardToJson(this);
}
