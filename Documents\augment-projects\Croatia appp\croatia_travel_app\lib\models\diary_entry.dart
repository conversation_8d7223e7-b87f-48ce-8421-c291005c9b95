import 'package:json_annotation/json_annotation.dart';

part 'diary_entry.g.dart';

@JsonSerializable()
class DiaryEntry {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final String? location;
  final double? latitude;
  final double? longitude;
  final List<String> photos;
  final List<String> voiceNotes; // IDs hlasových poznámek
  final List<String> videos; // IDs video záznamů
  final List<String> tags;
  final DiaryMood? mood;
  final String? weather;
  final double? rating;
  final bool isPrivate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DiaryEntry({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.location,
    this.latitude,
    this.longitude,
    this.photos = const [],
    this.voiceNotes = const [],
    this.videos = const [],
    this.tags = const [],
    this.mood,
    this.weather,
    this.rating,
    this.isPrivate = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DiaryEntry.fromJson(Map<String, dynamic> json) =>
      _$DiaryEntryFromJson(json);
  Map<String, dynamic> toJson() => _$DiaryEntryToJson(this);

  DiaryEntry copyWith({
    String? id,
    String? title,
    String? content,
    DateTime? date,
    String? location,
    double? latitude,
    double? longitude,
    List<String>? photos,
    List<String>? voiceNotes,
    List<String>? videos,
    List<String>? tags,
    DiaryMood? mood,
    String? weather,
    double? rating,
    bool? isPrivate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DiaryEntry(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      date: date ?? this.date,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      photos: photos ?? this.photos,
      voiceNotes: voiceNotes ?? this.voiceNotes,
      videos: videos ?? this.videos,
      tags: tags ?? this.tags,
      mood: mood ?? this.mood,
      weather: weather ?? this.weather,
      rating: rating ?? this.rating,
      isPrivate: isPrivate ?? this.isPrivate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  int get wordCount => content.split(' ').length;

  bool get hasPhotos => photos.isNotEmpty;

  bool get hasVoiceNotes => voiceNotes.isNotEmpty;

  bool get hasVideos => videos.isNotEmpty;

  bool get hasLocation => location != null && location!.isNotEmpty;

  bool get hasCoordinates => latitude != null && longitude != null;

  String get excerpt {
    if (content.length <= 100) return content;
    return '${content.substring(0, 97)}...';
  }
}

enum DiaryMood {
  veryHappy,
  happy,
  excited,
  content,
  neutral,
  sad,
  verySad,
  angry,
  anxious,
  stressed,
  relaxed,
  tired,
  adventurous,
  nostalgic,
}

extension DiaryMoodExtension on DiaryMood {
  String get displayName {
    switch (this) {
      case DiaryMood.veryHappy:
        return 'Velmi šťastný';
      case DiaryMood.happy:
        return 'Šťastný';
      case DiaryMood.excited:
        return 'Nadšený';
      case DiaryMood.content:
        return 'Spokojený';
      case DiaryMood.neutral:
        return 'Neutrální';
      case DiaryMood.sad:
        return 'Smutný';
      case DiaryMood.verySad:
        return 'Velmi smutný';
      case DiaryMood.angry:
        return 'Naštvaný';
      case DiaryMood.anxious:
        return 'Úzkostný';
      case DiaryMood.stressed:
        return 'Stresovaný';
      case DiaryMood.relaxed:
        return 'Uvolněný';
      case DiaryMood.tired:
        return 'Unavený';
      case DiaryMood.adventurous:
        return 'Dobrodružný';
      case DiaryMood.nostalgic:
        return 'Nostalgický';
    }
  }

  String get emoji {
    switch (this) {
      case DiaryMood.veryHappy:
        return '😄';
      case DiaryMood.happy:
        return '😊';
      case DiaryMood.excited:
        return '🤩';
      case DiaryMood.content:
        return '😌';
      case DiaryMood.neutral:
        return '😐';
      case DiaryMood.sad:
        return '😢';
      case DiaryMood.verySad:
        return '😭';
      case DiaryMood.angry:
        return '😠';
      case DiaryMood.anxious:
        return '😰';
      case DiaryMood.stressed:
        return '😫';
      case DiaryMood.relaxed:
        return '😌';
      case DiaryMood.tired:
        return '😴';
      case DiaryMood.adventurous:
        return '🤠';
      case DiaryMood.nostalgic:
        return '🥺';
    }
  }

  int get value {
    switch (this) {
      case DiaryMood.verySad:
        return 1;
      case DiaryMood.sad:
        return 2;
      case DiaryMood.neutral:
        return 3;
      case DiaryMood.happy:
        return 4;
      case DiaryMood.veryHappy:
        return 5;
      case DiaryMood.tired:
        return 2;
      case DiaryMood.relaxed:
        return 4;
      case DiaryMood.excited:
        return 5;
      case DiaryMood.adventurous:
        return 4;
      case DiaryMood.nostalgic:
        return 3;
    }
  }
}

// Šablony pro deníkové záznamy
class DiaryTemplates {
  static const List<Map<String, dynamic>> templates = [
    {
      'title': 'Nový den v {location}',
      'content':
          'Dnes jsem se probudil/a v {location} a...\n\nNejlepší zážitek dne:\n\nCo jsem se naučil/a:\n\nZítra plánuji:',
      'tags': ['cestování', 'nový den'],
    },
    {
      'title': 'Gastronomický zážitek',
      'content':
          'Dnes jsem ochutnal/a {dish} v {restaurant}.\n\nChuť:\nProstředí:\nCena:\nDoporučuji: ano/ne\n\nPoznámky:',
      'tags': ['jídlo', 'gastronomie'],
    },
    {
      'title': 'Návštěva památky',
      'content':
          'Navštívil/a jsem {monument} v {location}.\n\nPrvní dojem:\nHistorie:\nFotografie:\nTip pro ostatní:\n\nHodnocení:',
      'tags': ['památky', 'kultura'],
    },
    {
      'title': 'Přírodní zážitek',
      'content':
          'Dnes jsem byl/a v {nature_location}.\n\nPočasí:\nKrása přírody:\nAktivity:\nSetkání s lidmi:\n\nPocity:',
      'tags': ['příroda', 'outdoor'],
    },
    {
      'title': 'Reflexe dne',
      'content':
          'Jak hodnotím dnešní den:\n\nNejlepší moment:\nNejhorší moment:\nCo bych příště udělal/a jinak:\n\nVděčnost za:',
      'tags': ['reflexe', 'osobní rozvoj'],
    },
  ];
}

// Statistiky deníku
class DiaryStatistics {
  final int totalEntries;
  final int totalWords;
  final int totalPhotos;
  final Map<DiaryMood, int> moodDistribution;
  final List<String> mostUsedTags;
  final Map<String, int> entriesByLocation;
  final double averageRating;
  final int longestStreak;
  final int currentStreak;

  const DiaryStatistics({
    required this.totalEntries,
    required this.totalWords,
    required this.totalPhotos,
    required this.moodDistribution,
    required this.mostUsedTags,
    required this.entriesByLocation,
    required this.averageRating,
    required this.longestStreak,
    required this.currentStreak,
  });
}

// Exportní formáty
enum DiaryExportFormat { pdf, html, markdown, json, txt }

extension DiaryExportFormatExtension on DiaryExportFormat {
  String get displayName {
    switch (this) {
      case DiaryExportFormat.pdf:
        return 'PDF dokument';
      case DiaryExportFormat.html:
        return 'HTML stránka';
      case DiaryExportFormat.markdown:
        return 'Markdown soubor';
      case DiaryExportFormat.json:
        return 'JSON data';
      case DiaryExportFormat.txt:
        return 'Textový soubor';
    }
  }

  String get fileExtension {
    switch (this) {
      case DiaryExportFormat.pdf:
        return '.pdf';
      case DiaryExportFormat.html:
        return '.html';
      case DiaryExportFormat.markdown:
        return '.md';
      case DiaryExportFormat.json:
        return '.json';
      case DiaryExportFormat.txt:
        return '.txt';
    }
  }
}
