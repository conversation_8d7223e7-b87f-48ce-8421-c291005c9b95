import 'diary_entry.dart';

/// 📊 PERSONAL ANALYTICS MODELS - Modely pro osobní analytiku

/// Osobní dashboard
class PersonalDashboard {
  final String userId;
  final DateTime periodStart;
  final DateTime periodEnd;
  final WritingStatistics writingStatistics;
  final MoodAnalysis moodAnalysis;
  final LocationInsights locationInsights;
  final TimePatterns timePatterns;
  final ContentAnalysis contentAnalysis;
  final ProgressMetrics progressMetrics;
  final List<Achievement> achievements;
  final List<PersonalInsight> personalInsights;
  final DateTime generatedAt;

  const PersonalDashboard({
    required this.userId,
    required this.periodStart,
    required this.periodEnd,
    required this.writingStatistics,
    required this.moodAnalysis,
    required this.locationInsights,
    required this.timePatterns,
    required this.contentAnalysis,
    required this.progressMetrics,
    required this.achievements,
    required this.personalInsights,
    required this.generatedAt,
  });

  Duration get period => periodEnd.difference(periodStart);
  bool get hasData => writingStatistics.totalEntries > 0;

  factory PersonalDashboard.empty() {
    final now = DateTime.now();
    return PersonalDashboard(
      userId: '',
      periodStart: now.subtract(const Duration(days: 30)),
      periodEnd: now,
      writingStatistics: WritingStatistics.empty(),
      moodAnalysis: MoodAnalysis.empty(),
      locationInsights: LocationInsights.empty(),
      timePatterns: TimePatterns.empty(),
      contentAnalysis: ContentAnalysis.empty(),
      progressMetrics: ProgressMetrics.empty(),
      achievements: [],
      personalInsights: [],
      generatedAt: now,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'writingStatistics': writingStatistics.toJson(),
      'moodAnalysis': moodAnalysis.toJson(),
      'locationInsights': locationInsights.toJson(),
      'timePatterns': timePatterns.toJson(),
      'contentAnalysis': contentAnalysis.toJson(),
      'progressMetrics': progressMetrics.toJson(),
      'achievements': achievements.map((a) => a.toJson()).toList(),
      'personalInsights': personalInsights.map((i) => i.toJson()).toList(),
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

/// Statistiky psaní
class WritingStatistics {
  final int totalEntries;
  final int totalWords;
  final int totalCharacters;
  final double averageWordsPerEntry;
  final double averageCharactersPerEntry;
  final double writingFrequency; // zápisů za den
  final DiaryEntry? longestEntry;
  final DiaryEntry? shortestEntry;
  final int currentStreak;
  final int longestStreak;
  final Map<int, int> weeklyStats; // týden -> počet zápisů

  const WritingStatistics({
    required this.totalEntries,
    required this.totalWords,
    required this.totalCharacters,
    required this.averageWordsPerEntry,
    required this.averageCharactersPerEntry,
    required this.writingFrequency,
    this.longestEntry,
    this.shortestEntry,
    required this.currentStreak,
    required this.longestStreak,
    this.weeklyStats = const {},
  });

  String get productivityLevel {
    if (writingFrequency >= 1.0) return 'Velmi aktivní';
    if (writingFrequency >= 0.5) return 'Aktivní';
    if (writingFrequency >= 0.2) return 'Mírně aktivní';
    return 'Občasný';
  }

  String get streakStatus {
    if (currentStreak >= 30) return 'Mistr konzistence';
    if (currentStreak >= 14) return 'Skvělá série';
    if (currentStreak >= 7) return 'Týdenní série';
    if (currentStreak >= 3) return 'Dobrý začátek';
    return 'Začněte sérii';
  }

  factory WritingStatistics.empty() {
    return const WritingStatistics(
      totalEntries: 0,
      totalWords: 0,
      totalCharacters: 0,
      averageWordsPerEntry: 0.0,
      averageCharactersPerEntry: 0.0,
      writingFrequency: 0.0,
      currentStreak: 0,
      longestStreak: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalEntries': totalEntries,
      'totalWords': totalWords,
      'totalCharacters': totalCharacters,
      'averageWordsPerEntry': averageWordsPerEntry,
      'averageCharactersPerEntry': averageCharactersPerEntry,
      'writingFrequency': writingFrequency,
      'longestEntry': longestEntry?.toJson(),
      'shortestEntry': shortestEntry?.toJson(),
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'weeklyStats': weeklyStats,
    };
  }
}

/// Analýza nálad
class MoodAnalysis {
  final Map<DiaryMood, int> moodDistribution;
  final DiaryMood? dominantMood;
  final double averageMoodScore;
  final Map<DateTime, DiaryMood> moodTrends;
  final String trendAnalysis;
  final List<DateTime> bestDays;
  final List<DateTime> worstDays;
  final double moodStability; // 0-1, vyšší = stabilnější

  const MoodAnalysis({
    this.moodDistribution = const {},
    this.dominantMood,
    required this.averageMoodScore,
    this.moodTrends = const {},
    required this.trendAnalysis,
    this.bestDays = const [],
    this.worstDays = const [],
    required this.moodStability,
  });

  String get moodLevel {
    if (averageMoodScore >= 4.0) return 'Velmi pozitivní';
    if (averageMoodScore >= 3.0) return 'Pozitivní';
    if (averageMoodScore >= 2.0) return 'Neutrální';
    return 'Potřebuje pozornost';
  }

  String get stabilityLevel {
    if (moodStability >= 0.8) return 'Velmi stabilní';
    if (moodStability >= 0.6) return 'Stabilní';
    if (moodStability >= 0.4) return 'Mírně nestabilní';
    return 'Nestabilní';
  }

  factory MoodAnalysis.empty() {
    return const MoodAnalysis(
      averageMoodScore: 0.0,
      trendAnalysis: 'Nedostatek dat',
      moodStability: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'moodDistribution': moodDistribution.map((k, v) => MapEntry(k.name, v)),
      'dominantMood': dominantMood?.name,
      'averageMoodScore': averageMoodScore,
      'moodTrends': moodTrends.map((k, v) => MapEntry(k.toIso8601String(), v.name)),
      'trendAnalysis': trendAnalysis,
      'bestDays': bestDays.map((d) => d.toIso8601String()).toList(),
      'worstDays': worstDays.map((d) => d.toIso8601String()).toList(),
      'moodStability': moodStability,
    };
  }
}

/// Lokační insights
class LocationInsights {
  final List<MapEntry<String, int>> topLocations;
  final List<MapEntry<String, double>> happiestLocations;
  final int uniqueLocations;
  final Map<String, List<DiaryMood>> locationMoodMap;
  final List<String> travelPatterns;

  const LocationInsights({
    this.topLocations = const [],
    this.happiestLocations = const [],
    required this.uniqueLocations,
    this.locationMoodMap = const {},
    this.travelPatterns = const [],
  });

  String get travelStyle {
    if (uniqueLocations > 20) return 'Světoběžník';
    if (uniqueLocations > 10) return 'Aktivní cestovatel';
    if (uniqueLocations > 5) return 'Občasný cestovatel';
    return 'Domácí typ';
  }

  factory LocationInsights.empty() {
    return const LocationInsights(
      uniqueLocations: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'topLocations': topLocations.map((e) => {'location': e.key, 'count': e.value}).toList(),
      'happiestLocations': happiestLocations.map((e) => {'location': e.key, 'score': e.value}).toList(),
      'uniqueLocations': uniqueLocations,
      'locationMoodMap': locationMoodMap.map((k, v) => MapEntry(k, v.map((m) => m.name).toList())),
      'travelPatterns': travelPatterns,
    };
  }
}

/// Časové vzorce
class TimePatterns {
  final Map<int, int> hourlyDistribution; // hodina -> počet zápisů
  final Map<int, int> dayOfWeekDistribution; // den v týdnu -> počet zápisů
  final Map<int, int> monthlyDistribution; // měsíc -> počet zápisů
  final int peakWritingHour;
  final int peakWritingDay;
  final double writingConsistency; // 0-1, vyšší = konzistentnější

  const TimePatterns({
    this.hourlyDistribution = const {},
    this.dayOfWeekDistribution = const {},
    this.monthlyDistribution = const {},
    required this.peakWritingHour,
    required this.peakWritingDay,
    required this.writingConsistency,
  });

  String get peakTimeDescription {
    if (peakWritingHour >= 5 && peakWritingHour < 12) return 'Ranní ptáče';
    if (peakWritingHour >= 12 && peakWritingHour < 17) return 'Odpolední pisatel';
    if (peakWritingHour >= 17 && peakWritingHour < 22) return 'Večerní reflexe';
    return 'Noční sova';
  }

  String get peakDayDescription {
    const days = ['', 'Pondělí', 'Úterý', 'Středa', 'Čtvrtek', 'Pátek', 'Sobota', 'Neděle'];
    return days[peakWritingDay] ?? 'Neznámý';
  }

  factory TimePatterns.empty() {
    return const TimePatterns(
      peakWritingHour: 12,
      peakWritingDay: 1,
      writingConsistency: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hourlyDistribution': hourlyDistribution,
      'dayOfWeekDistribution': dayOfWeekDistribution,
      'monthlyDistribution': monthlyDistribution,
      'peakWritingHour': peakWritingHour,
      'peakWritingDay': peakWritingDay,
      'writingConsistency': writingConsistency,
    };
  }
}

/// Analýza obsahu
class ContentAnalysis {
  final List<MapEntry<String, int>> topWords;
  final List<MapEntry<String, int>> topTopics;
  final int vocabularySize;
  final Duration averageReadingTime;
  final double contentDiversity; // 0-1, vyšší = rozmanitější obsah

  const ContentAnalysis({
    this.topWords = const [],
    this.topTopics = const [],
    required this.vocabularySize,
    required this.averageReadingTime,
    required this.contentDiversity,
  });

  String get vocabularyLevel {
    if (vocabularySize > 1000) return 'Bohatá slovní zásoba';
    if (vocabularySize > 500) return 'Dobrá slovní zásoba';
    if (vocabularySize > 200) return 'Průměrná slovní zásoba';
    return 'Základní slovní zásoba';
  }

  String get diversityLevel {
    if (contentDiversity >= 0.8) return 'Velmi rozmanité';
    if (contentDiversity >= 0.6) return 'Rozmanité';
    if (contentDiversity >= 0.4) return 'Mírně rozmanité';
    return 'Monotematické';
  }

  factory ContentAnalysis.empty() {
    return const ContentAnalysis(
      vocabularySize: 0,
      averageReadingTime: Duration.zero,
      contentDiversity: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'topWords': topWords.map((e) => {'word': e.key, 'count': e.value}).toList(),
      'topTopics': topTopics.map((e) => {'topic': e.key, 'count': e.value}).toList(),
      'vocabularySize': vocabularySize,
      'averageReadingTime': averageReadingTime.inMinutes,
      'contentDiversity': contentDiversity,
    };
  }
}

/// Metriky pokroku
class ProgressMetrics {
  final int entriesThisMonth;
  final int entriesLastMonth;
  final double growthRate; // -1 až 1, pozitivní = růst
  final double consistencyScore; // 0-1
  final double qualityScore; // 0-1
  final double engagementScore; // 0-1

  const ProgressMetrics({
    required this.entriesThisMonth,
    required this.entriesLastMonth,
    required this.growthRate,
    required this.consistencyScore,
    required this.qualityScore,
    required this.engagementScore,
  });

  String get growthTrend {
    if (growthRate > 0.2) return 'Rychlý růst';
    if (growthRate > 0.0) return 'Mírný růst';
    if (growthRate > -0.2) return 'Stabilní';
    return 'Pokles';
  }

  double get overallScore => (consistencyScore + qualityScore + engagementScore) / 3;

  String get overallLevel {
    if (overallScore >= 0.8) return 'Výborný';
    if (overallScore >= 0.6) return 'Dobrý';
    if (overallScore >= 0.4) return 'Průměrný';
    return 'Potřebuje zlepšení';
  }

  factory ProgressMetrics.empty() {
    return const ProgressMetrics(
      entriesThisMonth: 0,
      entriesLastMonth: 0,
      growthRate: 0.0,
      consistencyScore: 0.0,
      qualityScore: 0.0,
      engagementScore: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entriesThisMonth': entriesThisMonth,
      'entriesLastMonth': entriesLastMonth,
      'growthRate': growthRate,
      'consistencyScore': consistencyScore,
      'qualityScore': qualityScore,
      'engagementScore': engagementScore,
    };
  }
}

/// Achievement/Úspěch
class Achievement {
  final String id;
  final String name;
  final String description;
  final String icon;
  final DateTime unlockedAt;
  final AchievementCategory category;
  final AchievementRarity rarity;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.unlockedAt,
    required this.category,
    required this.rarity,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'unlockedAt': unlockedAt.toIso8601String(),
      'category': category.name,
      'rarity': rarity.name,
    };
  }
}

/// Kategorie achievementů
enum AchievementCategory {
  milestone,    // Milníky
  consistency,  // Konzistence
  content,      // Obsah
  social,       // Sociální
  exploration,  // Objevování
}

/// Vzácnost achievementů
enum AchievementRarity {
  common,       // Běžné
  uncommon,     // Neobvyklé
  rare,         // Vzácné
  epic,         // Epické
  legendary,    // Legendární
}

/// Osobní insight
class PersonalInsight {
  final String id;
  final String title;
  final String description;
  final InsightType type;
  final InsightImportance importance;
  final bool actionable;
  final String? recommendation;

  const PersonalInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.importance,
    required this.actionable,
    this.recommendation,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'importance': importance.name,
      'actionable': actionable,
      'recommendation': recommendation,
    };
  }
}

/// Typ insightu
enum InsightType {
  mood,         // Nálada
  pattern,      // Vzorec
  content,      // Obsah
  behavior,     // Chování
  achievement,  // Úspěch
}

/// Důležitost insightu
enum InsightImportance {
  low,          // Nízká
  medium,       // Střední
  high,         // Vysoká
  critical,     // Kritická
}

/// Rozšíření pro DiaryMood
extension DiaryMoodAnalytics on DiaryMood {
  double get score {
    switch (this) {
      case DiaryMood.veryHappy:
        return 5.0;
      case DiaryMood.happy:
        return 4.0;
      case DiaryMood.excited:
        return 4.5;
      case DiaryMood.relaxed:
        return 3.5;
      case DiaryMood.neutral:
        return 3.0;
      case DiaryMood.sad:
        return 2.0;
      case DiaryMood.angry:
        return 1.5;
      case DiaryMood.anxious:
        return 1.0;
    }
  }
}

/// Analytics data pro cache
class AnalyticsData {
  final String key;
  final Map<String, dynamic> data;
  final DateTime cachedAt;
  final Duration validFor;

  const AnalyticsData({
    required this.key,
    required this.data,
    required this.cachedAt,
    required this.validFor,
  });

  bool get isValid => DateTime.now().difference(cachedAt) < validFor;

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'cachedAt': cachedAt.toIso8601String(),
      'validFor': validFor.inMilliseconds,
    };
  }
}
