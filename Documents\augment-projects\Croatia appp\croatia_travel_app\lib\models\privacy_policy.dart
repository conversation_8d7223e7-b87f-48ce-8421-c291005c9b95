/// 🔒 PRIVACY POLICY MODELS - GDPR Compliance modely

/// Privacy Policy dokument
class PrivacyPolicy {
  final String version;
  final DateTime effectiveDate;
  final DateTime lastUpdated;
  final String language;
  final List<PrivacyPolicySection> sections;

  const PrivacyPolicy({
    required this.version,
    required this.effectiveDate,
    required this.lastUpdated,
    required this.language,
    required this.sections,
  });

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'effectiveDate': effectiveDate.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'language': language,
      'sections': sections.map((s) => s.toJson()).toList(),
    };
  }
}

/// Sekce Privacy Policy
class PrivacyPolicySection {
  final String title;
  final String content;
  final bool isRequired;
  final List<String>? subSections;

  const PrivacyPolicySection({
    required this.title,
    required this.content,
    this.isRequired = true,
    this.subSections,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'isRequired': isRequired,
      'subSections': subSections,
    };
  }
}

/// Terms of Service dokument
class TermsOfService {
  final String version;
  final DateTime effectiveDate;
  final DateTime lastUpdated;
  final String language;
  final List<TermsOfServiceSection> sections;

  const TermsOfService({
    required this.version,
    required this.effectiveDate,
    required this.lastUpdated,
    required this.language,
    required this.sections,
  });

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'effectiveDate': effectiveDate.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'language': language,
      'sections': sections.map((s) => s.toJson()).toList(),
    };
  }
}

/// Sekce Terms of Service
class TermsOfServiceSection {
  final String title;
  final String content;
  final List<String>? subSections;

  const TermsOfServiceSection({
    required this.title,
    required this.content,
    this.subSections,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'subSections': subSections,
    };
  }
}

/// Typ souhlasu
enum ConsentType {
  dataProcessing,      // Zpracování osobních údajů
  marketing,           // Marketingová komunikace
  analytics,           // Analytické cookies
  thirdPartySharing,   // Sdílení s třetími stranami
  locationTracking,    // Sledování polohy
  biometricData,       // Biometrická data
  sensitiveData,       // Citlivé údaje
  automated,           // Automatizované rozhodování
}

extension ConsentTypeExtension on ConsentType {
  String get displayName {
    switch (this) {
      case ConsentType.dataProcessing:
        return 'Zpracování osobních údajů';
      case ConsentType.marketing:
        return 'Marketingová komunikace';
      case ConsentType.analytics:
        return 'Analytické cookies';
      case ConsentType.thirdPartySharing:
        return 'Sdílení s třetími stranami';
      case ConsentType.locationTracking:
        return 'Sledování polohy';
      case ConsentType.biometricData:
        return 'Biometrická data';
      case ConsentType.sensitiveData:
        return 'Citlivé údaje';
      case ConsentType.automated:
        return 'Automatizované rozhodování';
    }
  }

  String get description {
    switch (this) {
      case ConsentType.dataProcessing:
        return 'Souhlas se zpracováním vašich osobních údajů pro poskytování služby';
      case ConsentType.marketing:
        return 'Souhlas se zasíláním marketingových sdělení a nabídek';
      case ConsentType.analytics:
        return 'Souhlas s používáním analytických cookies pro zlepšení služby';
      case ConsentType.thirdPartySharing:
        return 'Souhlas se sdílením dat s partnerskými službami';
      case ConsentType.locationTracking:
        return 'Souhlas se sledováním vaší polohy pro lokalizované služby';
      case ConsentType.biometricData:
        return 'Souhlas se zpracováním biometrických údajů (např. rozpoznávání obličeje)';
      case ConsentType.sensitiveData:
        return 'Souhlas se zpracováním citlivých osobních údajů';
      case ConsentType.automated:
        return 'Souhlas s automatizovaným rozhodováním na základě vašich dat';
    }
  }

  bool get isRequired {
    switch (this) {
      case ConsentType.dataProcessing:
        return true; // Nutné pro poskytování služby
      case ConsentType.marketing:
      case ConsentType.analytics:
      case ConsentType.thirdPartySharing:
      case ConsentType.locationTracking:
      case ConsentType.biometricData:
      case ConsentType.sensitiveData:
      case ConsentType.automated:
        return false; // Volitelné
    }
  }
}

/// Záznam souhlasu
class ConsentRecord {
  final String id;
  final String userId;
  final ConsentType type;
  final bool granted;
  final DateTime timestamp;
  final String ipAddress;
  final String userAgent;
  final String? specificPurpose;
  final Map<String, dynamic> metadata;

  const ConsentRecord({
    required this.id,
    required this.userId,
    required this.type,
    required this.granted,
    required this.timestamp,
    required this.ipAddress,
    required this.userAgent,
    this.specificPurpose,
    this.metadata = const {},
  });

  ConsentRecord copyWith({
    String? id,
    String? userId,
    ConsentType? type,
    bool? granted,
    DateTime? timestamp,
    String? ipAddress,
    String? userAgent,
    String? specificPurpose,
    Map<String, dynamic>? metadata,
  }) {
    return ConsentRecord(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      granted: granted ?? this.granted,
      timestamp: timestamp ?? this.timestamp,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      specificPurpose: specificPurpose ?? this.specificPurpose,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.name,
      'granted': granted,
      'timestamp': timestamp.toIso8601String(),
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'specificPurpose': specificPurpose,
      'metadata': metadata,
    };
  }
}

/// Souhlas uživatele
class UserConsent {
  final String userId;
  final List<ConsentRecord> consents;
  final DateTime lastUpdated;

  const UserConsent({
    required this.userId,
    required this.consents,
    required this.lastUpdated,
  });

  UserConsent copyWith({
    String? userId,
    List<ConsentRecord>? consents,
    DateTime? lastUpdated,
  }) {
    return UserConsent(
      userId: userId ?? this.userId,
      consents: consents ?? this.consents,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Získá nejnovější consent pro daný typ
  ConsentRecord? getLatestConsent(ConsentType type) {
    final relevantConsents = consents.where((c) => c.type == type).toList();
    if (relevantConsents.isEmpty) return null;
    
    relevantConsents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return relevantConsents.first;
  }

  /// Kontrola, zda má uživatel platný souhlas
  bool hasValidConsent(ConsentType type) {
    final latest = getLatestConsent(type);
    if (latest == null) return false;
    
    // Kontrola platnosti (2 roky)
    final isValid = DateTime.now().difference(latest.timestamp).inDays < 730;
    return latest.granted && isValid;
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'consents': consents.map((c) => c.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Typ události souhlasu
enum ConsentEventType {
  consentRecorded,     // Souhlas zaznamenán
  consentRevoked,      // Souhlas odvolán
  consentExpired,      // Souhlas vypršel
  dataExported,        // Data exportována
  dataDeleted,         // Data smazána
  policyUpdated,       // Policy aktualizována
}

/// Událost souhlasu
class ConsentEvent {
  final ConsentEventType type;
  final ConsentType? consentType;
  final bool? granted;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const ConsentEvent({
    required this.type,
    this.consentType,
    this.granted,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'consentType': consentType?.name,
      'granted': granted,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Export formát
enum ExportFormat {
  json,
  xml,
  csv,
  pdf,
}

/// Export uživatelských dat
class UserDataExport {
  final String userId;
  final DateTime exportDate;
  final ExportFormat format;
  final Map<String, dynamic> data;
  final Map<String, dynamic> metadata;

  const UserDataExport({
    required this.userId,
    required this.exportDate,
    required this.format,
    required this.data,
    this.metadata = const {},
  });

  /// Velikost exportu v MB
  double get estimatedSizeMB {
    // Odhad na základě počtu položek
    final totalEntries = metadata['total_entries'] as int? ?? 0;
    final totalPhotos = metadata['total_photos'] as int? ?? 0;
    
    // Odhad: 1KB per entry, 2MB per photo
    final entriesSize = totalEntries * 1024; // bytes
    final photosSize = totalPhotos * 2 * 1024 * 1024; // bytes
    
    return (entriesSize + photosSize) / (1024 * 1024); // MB
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'exportDate': exportDate.toIso8601String(),
      'format': format.name,
      'data': data,
      'metadata': metadata,
    };
  }
}

/// Cookie kategorie
enum CookieCategory {
  necessary,    // Nezbytné
  functional,   // Funkční
  analytics,    // Analytické
  marketing,    // Marketingové
}

extension CookieCategoryExtension on CookieCategory {
  String get displayName {
    switch (this) {
      case CookieCategory.necessary:
        return 'Nezbytné cookies';
      case CookieCategory.functional:
        return 'Funkční cookies';
      case CookieCategory.analytics:
        return 'Analytické cookies';
      case CookieCategory.marketing:
        return 'Marketingové cookies';
    }
  }

  String get description {
    switch (this) {
      case CookieCategory.necessary:
        return 'Nezbytné pro základní funkčnost aplikace';
      case CookieCategory.functional:
        return 'Zapamatování vašich preferencí a nastavení';
      case CookieCategory.analytics:
        return 'Měření výkonu a používání aplikace';
      case CookieCategory.marketing:
        return 'Personalizované reklamy a marketing';
    }
  }

  bool get isRequired {
    return this == CookieCategory.necessary;
  }
}

/// Cookie nastavení
class CookieSettings {
  final Map<CookieCategory, bool> preferences;
  final DateTime lastUpdated;

  const CookieSettings({
    required this.preferences,
    required this.lastUpdated,
  });

  CookieSettings copyWith({
    Map<CookieCategory, bool>? preferences,
    DateTime? lastUpdated,
  }) {
    return CookieSettings(
      preferences: preferences ?? this.preferences,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool isAllowed(CookieCategory category) {
    return preferences[category] ?? category.isRequired;
  }

  Map<String, dynamic> toJson() {
    return {
      'preferences': preferences.map((k, v) => MapEntry(k.name, v)),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Data retention policy
class DataRetentionPolicy {
  final Map<String, Duration> retentionPeriods;
  final DateTime effectiveDate;

  const DataRetentionPolicy({
    required this.retentionPeriods,
    required this.effectiveDate,
  });

  Duration? getRetentionPeriod(String dataType) {
    return retentionPeriods[dataType];
  }

  bool isExpired(String dataType, DateTime createdAt) {
    final retention = getRetentionPeriod(dataType);
    if (retention == null) return false;
    
    return DateTime.now().difference(createdAt) > retention;
  }

  static DataRetentionPolicy get defaultPolicy {
    return DataRetentionPolicy(
      retentionPeriods: {
        'diary_entries': const Duration(days: 3650), // 10 let
        'photos': const Duration(days: 3650), // 10 let
        'analytics': const Duration(days: 1095), // 3 roky
        'logs': const Duration(days: 90), // 3 měsíce
        'consents': const Duration(days: 2555), // 7 let (právní požadavek)
        'deleted_accounts': const Duration(days: 90), // 3 měsíce
      },
      effectiveDate: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'retentionPeriods': retentionPeriods.map(
        (k, v) => MapEntry(k, v.inDays),
      ),
      'effectiveDate': effectiveDate.toIso8601String(),
    };
  }
}
