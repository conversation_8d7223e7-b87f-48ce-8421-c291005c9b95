/// 🏢 BUSINESS MODELS - Modely pro B2B systém

/// Business profil
class BusinessProfile {
  final String id;
  final String name;
  final String description;
  final BusinessType type;
  final BusinessCategory category;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String email;
  final String website;
  final List<String> images;
  final BusinessHours openingHours;
  final List<String> amenities;
  final double rating;
  final int reviewCount;
  final bool isVerified;
  final bool isPremium;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final BusinessOwner owner;
  final BusinessSubscription? subscription;

  const BusinessProfile({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    required this.email,
    required this.website,
    this.images = const [],
    required this.openingHours,
    this.amenities = const [],
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isVerified = false,
    this.isPremium = false,
    required this.createdAt,
    required this.lastUpdated,
    required this.owner,
    this.subscription,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'category': category.name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'email': email,
      'website': website,
      'images': images,
      'openingHours': openingHours.toJson(),
      'amenities': amenities,
      'rating': rating,
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'owner': owner.toJson(),
      'subscription': subscription?.toJson(),
    };
  }

  factory BusinessProfile.fromJson(Map<String, dynamic> json) {
    return BusinessProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: BusinessType.values.firstWhere((e) => e.name == json['type']),
      category: BusinessCategory.values.firstWhere((e) => e.name == json['category']),
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      phone: json['phone'] as String,
      email: json['email'] as String,
      website: json['website'] as String,
      images: (json['images'] as List<dynamic>).cast<String>(),
      openingHours: BusinessHours.fromJson(json['openingHours'] as Map<String, dynamic>),
      amenities: (json['amenities'] as List<dynamic>).cast<String>(),
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      isVerified: json['isVerified'] as bool,
      isPremium: json['isPremium'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      owner: BusinessOwner.fromJson(json['owner'] as Map<String, dynamic>),
      subscription: json['subscription'] != null
          ? BusinessSubscription.fromJson(json['subscription'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// Typ podnikání
enum BusinessType {
  restaurant,
  hotel,
  attraction,
  shop,
  service,
  transport,
  entertainment,
  healthcare,
  education,
  other,
}

/// Kategorie podnikání
enum BusinessCategory {
  food,
  accommodation,
  tourism,
  retail,
  automotive,
  beauty,
  fitness,
  technology,
  finance,
  consulting,
  marketing,
  events,
}

/// Majitel podniku
class BusinessOwner {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final DateTime registeredAt;

  const BusinessOwner({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.registeredAt,
  });

  String get fullName => '$firstName $lastName';

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'registeredAt': registeredAt.toIso8601String(),
    };
  }

  factory BusinessOwner.fromJson(Map<String, dynamic> json) {
    return BusinessOwner(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      registeredAt: DateTime.parse(json['registeredAt'] as String),
    );
  }
}

/// Otevírací hodiny
class BusinessHours {
  final Map<String, DayHours> schedule;

  const BusinessHours({required this.schedule});

  bool isOpenNow() {
    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    final dayHours = schedule[dayName];
    
    if (dayHours == null || !dayHours.isOpen) return false;
    
    final currentTime = now.hour * 60 + now.minute;
    return currentTime >= dayHours.openMinutes && currentTime <= dayHours.closeMinutes;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'monday';
      case 2: return 'tuesday';
      case 3: return 'wednesday';
      case 4: return 'thursday';
      case 5: return 'friday';
      case 6: return 'saturday';
      case 7: return 'sunday';
      default: return 'monday';
    }
  }

  Map<String, dynamic> toJson() {
    return schedule.map((key, value) => MapEntry(key, value.toJson()));
  }

  factory BusinessHours.fromJson(Map<String, dynamic> json) {
    return BusinessHours(
      schedule: json.map((key, value) => 
        MapEntry(key, DayHours.fromJson(value as Map<String, dynamic>))
      ),
    );
  }
}

/// Hodiny pro jeden den
class DayHours {
  final bool isOpen;
  final String? openTime;
  final String? closeTime;

  const DayHours({
    required this.isOpen,
    this.openTime,
    this.closeTime,
  });

  int get openMinutes {
    if (!isOpen || openTime == null) return 0;
    final parts = openTime!.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  int get closeMinutes {
    if (!isOpen || closeTime == null) return 0;
    final parts = closeTime!.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  Map<String, dynamic> toJson() {
    return {
      'isOpen': isOpen,
      'openTime': openTime,
      'closeTime': closeTime,
    };
  }

  factory DayHours.fromJson(Map<String, dynamic> json) {
    return DayHours(
      isOpen: json['isOpen'] as bool,
      openTime: json['openTime'] as String?,
      closeTime: json['closeTime'] as String?,
    );
  }
}

/// Business předplatné
class BusinessSubscription {
  final String id;
  final SubscriptionTier tier;
  final DateTime startDate;
  final DateTime endDate;
  final double monthlyPrice;
  final bool isActive;
  final List<String> features;

  const BusinessSubscription({
    required this.id,
    required this.tier,
    required this.startDate,
    required this.endDate,
    required this.monthlyPrice,
    required this.isActive,
    this.features = const [],
  });

  bool get isExpired => DateTime.now().isAfter(endDate);
  int get daysRemaining => endDate.difference(DateTime.now()).inDays;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tier': tier.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'monthlyPrice': monthlyPrice,
      'isActive': isActive,
      'features': features,
    };
  }

  factory BusinessSubscription.fromJson(Map<String, dynamic> json) {
    return BusinessSubscription(
      id: json['id'] as String,
      tier: SubscriptionTier.values.firstWhere((e) => e.name == json['tier']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      monthlyPrice: (json['monthlyPrice'] as num).toDouble(),
      isActive: json['isActive'] as bool,
      features: (json['features'] as List<dynamic>).cast<String>(),
    );
  }
}

/// Úroveň předplatného
enum SubscriptionTier {
  basic,
  premium,
  enterprise,
}

/// Business analytics
class BusinessAnalytics {
  final String businessId;
  final DateTime periodStart;
  final DateTime periodEnd;
  final int totalViews;
  final int totalClicks;
  final int totalBookings;
  final double revenue;
  final double averageRating;
  final int newReviews;
  final Map<String, int> trafficSources;
  final Map<String, int> popularTimes;
  final List<String> topSearchKeywords;

  const BusinessAnalytics({
    required this.businessId,
    required this.periodStart,
    required this.periodEnd,
    required this.totalViews,
    required this.totalClicks,
    required this.totalBookings,
    required this.revenue,
    required this.averageRating,
    required this.newReviews,
    this.trafficSources = const {},
    this.popularTimes = const {},
    this.topSearchKeywords = const [],
  });

  double get clickThroughRate => totalViews > 0 ? totalClicks / totalViews : 0.0;
  double get conversionRate => totalClicks > 0 ? totalBookings / totalClicks : 0.0;

  Map<String, dynamic> toJson() {
    return {
      'businessId': businessId,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'totalViews': totalViews,
      'totalClicks': totalClicks,
      'totalBookings': totalBookings,
      'revenue': revenue,
      'averageRating': averageRating,
      'newReviews': newReviews,
      'trafficSources': trafficSources,
      'popularTimes': popularTimes,
      'topSearchKeywords': topSearchKeywords,
    };
  }

  factory BusinessAnalytics.fromJson(Map<String, dynamic> json) {
    return BusinessAnalytics(
      businessId: json['businessId'] as String,
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      totalViews: json['totalViews'] as int,
      totalClicks: json['totalClicks'] as int,
      totalBookings: json['totalBookings'] as int,
      revenue: (json['revenue'] as num).toDouble(),
      averageRating: (json['averageRating'] as num).toDouble(),
      newReviews: json['newReviews'] as int,
      trafficSources: Map<String, int>.from(json['trafficSources'] as Map),
      popularTimes: Map<String, int>.from(json['popularTimes'] as Map),
      topSearchKeywords: (json['topSearchKeywords'] as List<dynamic>).cast<String>(),
    );
  }
}

/// Business booking
class BusinessBooking {
  final String id;
  final String businessId;
  final String customerId;
  final String customerName;
  final String customerEmail;
  final String customerPhone;
  final DateTime bookingDate;
  final DateTime serviceDate;
  final String serviceType;
  final int numberOfPeople;
  final double totalPrice;
  final BookingStatus status;
  final String? notes;
  final DateTime createdAt;

  const BusinessBooking({
    required this.id,
    required this.businessId,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.customerPhone,
    required this.bookingDate,
    required this.serviceDate,
    required this.serviceType,
    required this.numberOfPeople,
    required this.totalPrice,
    required this.status,
    this.notes,
    required this.createdAt,
  });

  bool get isUpcoming => serviceDate.isAfter(DateTime.now());
  bool get isPast => serviceDate.isBefore(DateTime.now());

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'businessId': businessId,
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'customerPhone': customerPhone,
      'bookingDate': bookingDate.toIso8601String(),
      'serviceDate': serviceDate.toIso8601String(),
      'serviceType': serviceType,
      'numberOfPeople': numberOfPeople,
      'totalPrice': totalPrice,
      'status': status.name,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory BusinessBooking.fromJson(Map<String, dynamic> json) {
    return BusinessBooking(
      id: json['id'] as String,
      businessId: json['businessId'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      customerEmail: json['customerEmail'] as String,
      customerPhone: json['customerPhone'] as String,
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      serviceDate: DateTime.parse(json['serviceDate'] as String),
      serviceType: json['serviceType'] as String,
      numberOfPeople: json['numberOfPeople'] as int,
      totalPrice: (json['totalPrice'] as num).toDouble(),
      status: BookingStatus.values.firstWhere((e) => e.name == json['status']),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }
}

/// Stav rezervace
enum BookingStatus {
  pending,
  confirmed,
  cancelled,
  completed,
  noShow,
}
