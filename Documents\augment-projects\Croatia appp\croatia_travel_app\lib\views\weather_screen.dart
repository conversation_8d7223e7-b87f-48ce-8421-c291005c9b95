import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class WeatherScreen extends StatefulWidget {
  const WeatherScreen({super.key});

  @override
  State<WeatherScreen> createState() => _WeatherScreenState();
}

class _WeatherScreenState extends State<WeatherScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Aktuální počasí
  String _currentLocation = 'Zagreb';
  double _currentTemp = 22.0;
  String _currentCondition = 'Slunečno';
  IconData _currentIcon = Icons.wb_sunny;
  int _humidity = 65;
  int _uvIndex = 7;
  double _seaTemp = 24.5;
  int _airQualityIndex = 45; // AQI 0-100
  String _airQualityLevel = 'Dobrá';
  List<String> _pollutants = [
    'PM2.5: 12 μg/m³',
    'PM10: 18 μg/m³',
    'O3: 85 μg/m³',
  ];
  int _windSpeed = 12;
  String _windDirection = 'SZ';

  // Předpověď
  List<WeatherForecast> _forecast = [];
  List<HourlyForecast> _hourlyForecast = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadWeatherData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadWeatherData() async {
    // Simulace načtení dat o počasí
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _forecast = [
        WeatherForecast('Dnes', 22, 18, Icons.wb_sunny, 'Slunečno', 7, 65),
        WeatherForecast('Zítra', 24, 19, Icons.wb_sunny, 'Slunečno', 8, 60),
        WeatherForecast('Po', 20, 15, Icons.cloud, 'Oblačno', 4, 75),
        WeatherForecast('Út', 18, 12, Icons.grain, 'Déšť', 2, 85),
        WeatherForecast(
          'St',
          21,
          16,
          Icons.wb_sunny_outlined,
          'Polojasno',
          6,
          70,
        ),
        WeatherForecast('Čt', 23, 17, Icons.wb_sunny, 'Slunečno', 7, 62),
        WeatherForecast('Pá', 25, 20, Icons.wb_sunny, 'Slunečno', 8, 58),
      ];

      _hourlyForecast = [
        HourlyForecast('12:00', 22, Icons.wb_sunny, 0),
        HourlyForecast('13:00', 23, Icons.wb_sunny, 0),
        HourlyForecast('14:00', 24, Icons.wb_sunny, 0),
        HourlyForecast('15:00', 25, Icons.wb_sunny_outlined, 0),
        HourlyForecast('16:00', 24, Icons.cloud, 10),
        HourlyForecast('17:00', 23, Icons.cloud, 20),
        HourlyForecast('18:00', 22, Icons.grain, 60),
        HourlyForecast('19:00', 21, Icons.grain, 80),
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Počasí v Chorvatsku',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFFFFB74D).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorWeatherHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.location_on),
              onSelected: (location) {
                setState(() {
                  _currentLocation = location;
                });
                _loadWeatherData();
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'Zagreb', child: Text('Zagreb')),
                const PopupMenuItem(value: 'Split', child: Text('Split')),
                const PopupMenuItem(
                  value: 'Dubrovnik',
                  child: Text('Dubrovnik'),
                ),
                const PopupMenuItem(value: 'Rijeka', child: Text('Rijeka')),
                const PopupMenuItem(value: 'Zadar', child: Text('Zadar')),
                const PopupMenuItem(value: 'Pula', child: Text('Pula')),
              ],
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.wb_sunny), text: 'Aktuální'),
            Tab(icon: Icon(Icons.calendar_today), text: 'Předpověď'),
            Tab(icon: Icon(Icons.access_time), text: 'Hodinová'),
            Tab(icon: Icon(Icons.waves), text: 'Moře'),
            Tab(icon: Icon(Icons.air), text: 'Vzduch'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCurrentWeatherTab(),
          _buildForecastTab(),
          _buildHourlyTab(),
          _buildSeaConditionsTab(),
          _buildAirQualityTab(),
        ],
      ),
    );
  }

  Widget _buildCurrentWeatherTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorWeatherBackgroundPainter(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Hlavní počasí karta
              Container(
                child: CustomPaint(
                  painter: WatercolorWeatherMainCardPainter(
                    const Color(0xFF006994),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        Text(
                          _currentLocation,
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF006994),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: CustomPaint(
                                painter: WatercolorWeatherIconPainter(
                                  const Color(0xFFFFB74D),
                                ),
                                child: Icon(
                                  _currentIcon,
                                  size: 80,
                                  color: const Color(0xFFFFB74D),
                                ),
                              ),
                            ),
                            const SizedBox(width: 24),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${_currentTemp.round()}°C',
                                  style: GoogleFonts.playfairDisplay(
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF006994),
                                  ),
                                ),
                                Text(
                                  _currentCondition,
                                  style: GoogleFonts.inter(
                                    fontSize: 16,
                                    color: const Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Detaily počasí
              Row(
                children: [
                  Expanded(
                    child: _buildWeatherDetailCard(
                      'Vlhkost',
                      '$_humidity%',
                      Icons.water_drop,
                      const Color(0xFF2E8B8B),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildWeatherDetailCard(
                      'UV Index',
                      '$_uvIndex',
                      Icons.wb_sunny,
                      const Color(0xFFFF6B35),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildWeatherDetailCard(
                      'Vítr',
                      '$_windSpeed km/h $_windDirection',
                      Icons.air,
                      const Color(0xFF006994),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildWeatherDetailCard(
                      'Moře',
                      '${_seaTemp.round()}°C',
                      Icons.waves,
                      const Color(0xFF2E8B8B),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherDetailCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorWeatherDetailCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeaConditionsTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorSeaBackgroundPainter(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Teplota moře
              Container(
                child: CustomPaint(
                  painter: WatercolorSeaMainCardPainter(
                    const Color(0xFF2E8B8B),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Teplota moře',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2E8B8B),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: CustomPaint(
                                painter: WatercolorSeaIconPainter(
                                  const Color(0xFF2E8B8B),
                                ),
                                child: Icon(
                                  Icons.waves,
                                  size: 60,
                                  color: const Color(0xFF2E8B8B),
                                ),
                              ),
                            ),
                            const SizedBox(width: 24),
                            Text(
                              '${_seaTemp.round()}°C',
                              style: GoogleFonts.playfairDisplay(
                                fontSize: 42,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF2E8B8B),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Ideální pro koupání',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Podmínky moře
              Row(
                children: [
                  Expanded(
                    child: _buildSeaDetailCard(
                      'Vlny',
                      '0.5m',
                      Icons.water,
                      const Color(0xFF2E8B8B),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSeaDetailCard(
                      'Vítr',
                      '12 km/h',
                      Icons.air,
                      const Color(0xFF006994),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildSeaDetailCard(
                      'Viditelnost',
                      '15 km',
                      Icons.visibility,
                      const Color(0xFFFFB74D),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSeaDetailCard(
                      'Příboj',
                      'Slabý',
                      Icons.waves,
                      const Color(0xFF2E8B8B),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeaDetailCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorSeaDetailCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Icon(icon, size: 28, color: color),
              const SizedBox(height: 8),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForecastTab() {
    return CustomPaint(
      painter: WatercolorWeatherBackgroundPainter(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _forecast.length,
        itemBuilder: (context, index) {
          final forecast = _forecast[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: CustomPaint(
              painter: WatercolorForecastCardPainter(
                _getWeatherColor(forecast.icon),
              ),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 60,
                      child: Text(
                        forecast.day,
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2C2C2C),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      forecast.icon,
                      size: 32,
                      color: _getWeatherColor(forecast.icon),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        forecast.condition,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${forecast.maxTemp}°',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF006994),
                          ),
                        ),
                        Text(
                          '${forecast.minTemp}°',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHourlyTab() {
    return CustomPaint(
      painter: WatercolorWeatherBackgroundPainter(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _hourlyForecast.length,
        itemBuilder: (context, index) {
          final forecast = _hourlyForecast[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: CustomPaint(
              painter: WatercolorHourlyCardPainter(
                _getWeatherColor(forecast.icon),
              ),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 50,
                      child: Text(
                        forecast.time,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF2C2C2C),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      forecast.icon,
                      size: 24,
                      color: _getWeatherColor(forecast.icon),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${forecast.temp}°C',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF006994),
                      ),
                    ),
                    const Spacer(),
                    if (forecast.rainChance > 0) ...[
                      Icon(
                        Icons.water_drop,
                        size: 16,
                        color: const Color(0xFF2E8B8B),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${forecast.rainChance}%',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF2E8B8B),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAirQualityTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorWeatherBackgroundPainter(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Hlavní AQI karta
              Container(
                child: CustomPaint(
                  painter: WatercolorWeatherMainCardPainter(
                    _getAirQualityColor(_airQualityIndex),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Kvalita vzduchu',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: _getAirQualityColor(_airQualityIndex),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: CustomPaint(
                                painter: WatercolorWeatherIconPainter(
                                  _getAirQualityColor(_airQualityIndex),
                                ),
                                child: Icon(
                                  Icons.air,
                                  size: 60,
                                  color: _getAirQualityColor(_airQualityIndex),
                                ),
                              ),
                            ),
                            const SizedBox(width: 24),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '$_airQualityIndex',
                                  style: GoogleFonts.playfairDisplay(
                                    fontSize: 42,
                                    fontWeight: FontWeight.bold,
                                    color: _getAirQualityColor(
                                      _airQualityIndex,
                                    ),
                                  ),
                                ),
                                Text(
                                  _airQualityLevel,
                                  style: GoogleFonts.inter(
                                    fontSize: 16,
                                    color: const Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getAirQualityDescription(_airQualityIndex),
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Znečišťující látky
              Container(
                child: CustomPaint(
                  painter: WatercolorWeatherDetailCardPainter(
                    const Color(0xFF666666),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Znečišťující látky',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C2C2C),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ..._pollutants
                            .map(
                              (pollutant) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.circle,
                                      size: 8,
                                      color: _getPollutantColor(pollutant),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      pollutant,
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        color: const Color(0xFF666666),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Doporučení
              Container(
                child: CustomPaint(
                  painter: WatercolorWeatherDetailCardPainter(
                    const Color(0xFF4CAF50),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: const Color(0xFF4CAF50),
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Doporučení',
                              style: GoogleFonts.playfairDisplay(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF4CAF50),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _getAirQualityRecommendation(_airQualityIndex),
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getAirQualityColor(int aqi) {
    if (aqi <= 50) return const Color(0xFF4CAF50); // Dobrá - zelená
    if (aqi <= 100) return const Color(0xFFFFEB3B); // Střední - žlutá
    if (aqi <= 150)
      return const Color(0xFFFF9800); // Nezdravá pro citlivé - oranžová
    if (aqi <= 200) return const Color(0xFFF44336); // Nezdravá - červená
    if (aqi <= 300) return const Color(0xFF9C27B0); // Velmi nezdravá - fialová
    return const Color(0xFF795548); // Nebezpečná - hnědá
  }

  String _getAirQualityDescription(int aqi) {
    if (aqi <= 50)
      return 'Kvalita vzduchu je dobrá. Ideální pro venkovní aktivity.';
    if (aqi <= 100) return 'Kvalita vzduchu je přijatelná pro většinu lidí.';
    if (aqi <= 150) return 'Citlivé osoby mohou pociťovat zdravotní problémy.';
    if (aqi <= 200) return 'Každý může začít pociťovat zdravotní problémy.';
    if (aqi <= 300)
      return 'Zdravotní varování. Každý může pociťovat vážnější účinky.';
    return 'Zdravotní poplach. Každý může pociťovat vážné zdravotní účinky.';
  }

  String _getAirQualityRecommendation(int aqi) {
    if (aqi <= 50)
      return 'Perfektní den pro venkovní aktivity! Užijte si čerstvý vzduch.';
    if (aqi <= 100)
      return 'Venkovní aktivity jsou v pořádku. Citlivé osoby by měly být opatrné.';
    if (aqi <= 150)
      return 'Omezte dlouhodobé venkovní aktivity, zejména pokud jste citliví na znečištění.';
    if (aqi <= 200)
      return 'Vyhněte se dlouhodobým venkovním aktivitám. Používejte roušku při výstupu ven.';
    if (aqi <= 300)
      return 'Zůstaňte uvnitř. Pokud musíte ven, používejte kvalitní respirátor.';
    return 'Zůstaňte uvnitř a zavřete okna. Vyhněte se jakýmkoli venkovním aktivitám.';
  }

  Color _getPollutantColor(String pollutant) {
    if (pollutant.contains('PM2.5')) return const Color(0xFFF44336);
    if (pollutant.contains('PM10')) return const Color(0xFFFF9800);
    if (pollutant.contains('O3')) return const Color(0xFF2196F3);
    return const Color(0xFF666666);
  }

  Color _getWeatherColor(IconData icon) {
    if (icon == Icons.wb_sunny) return const Color(0xFFFFB74D);
    if (icon == Icons.wb_sunny_outlined) return const Color(0xFFFFB74D);
    if (icon == Icons.cloud) return const Color(0xFF666666);
    if (icon == Icons.grain) return const Color(0xFF2E8B8B);
    return const Color(0xFF006994);
  }
}

// Modely pro počasí
class WeatherForecast {
  final String day;
  final int maxTemp;
  final int minTemp;
  final IconData icon;
  final String condition;
  final int uvIndex;
  final int humidity;

  WeatherForecast(
    this.day,
    this.maxTemp,
    this.minTemp,
    this.icon,
    this.condition,
    this.uvIndex,
    this.humidity,
  );
}

class HourlyForecast {
  final String time;
  final int temp;
  final IconData icon;
  final int rainChance;

  HourlyForecast(this.time, this.temp, this.icon, this.rainChance);
}

// Watercolor painters pro Weather Screen
class WatercolorWeatherHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Weather header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFFB74D).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorWeatherBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí počasí
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.02,
      size.width * 0.9,
      size.height * 0.08,
    );
    path.lineTo(size.width * 0.95, size.height * 0.95);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.98,
      size.width * 0.05,
      size.height * 0.92,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorWeatherMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorWeatherMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní počasí kartu
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorWeatherIconPainter extends CustomPainter {
  final Color color;

  WatercolorWeatherIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem weather ikony
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 60) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorWeatherDetailCardPainter extends CustomPainter {
  final Color color;

  WatercolorWeatherDetailCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro detail karty
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.2,
      size.width * 0.92,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.8,
      size.width * 0.08,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorForecastCardPainter extends CustomPainter {
  final Color color;

  WatercolorForecastCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro forecast karty
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.03,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorHourlyCardPainter extends CustomPainter {
  final Color color;

  WatercolorHourlyCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro hodinové karty
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.3);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.1,
      size.width * 0.8,
      size.height * 0.25,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.4,
      size.width * 0.95,
      size.height * 0.7,
    );
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.9,
      size.width * 0.1,
      size.height * 0.75,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.02,
      size.height * 0.3,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSeaBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro moře pozadí
    final path1 = Path();
    path1.moveTo(0, size.height * 0.2);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.6,
      size.height * 0.3,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.5,
      size.width,
      size.height * 0.15,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.08);
    canvas.drawPath(path1, paint);

    // Druhá vrstva vln
    final path2 = Path();
    path2.moveTo(0, size.height * 0.4);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.5,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.7,
      size.width,
      size.height * 0.3,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSeaMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorSeaMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní moře kartu
    final path = Path();
    path.moveTo(size.width * 0.06, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.18,
      size.width * 0.97,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.82,
      size.width * 0.06,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);

    // Vnitřní vlny
    final path2 = Path();
    path2.moveTo(size.width * 0.15, size.height * 0.25);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.1,
      size.width * 0.85,
      size.height * 0.2,
    );
    path2.lineTo(size.width * 0.8, size.height * 0.75);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.9,
      size.width * 0.2,
      size.height * 0.8,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSeaIconPainter extends CustomPainter {
  final Color color;

  WatercolorSeaIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny kolem sea ikony
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.2;

    final path = Path();
    for (int i = 0; i < 360; i += 15) {
      final angle = i * pi / 180;
      final variation = 0.7 + (sin(i * pi / 30) * 0.3);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.25);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSeaDetailCardPainter extends CustomPainter {
  final Color color;

  WatercolorSeaDetailCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro sea detail karty
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.18);
    path.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.08,
      size.width * 0.85,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.92,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.92,
      size.width * 0.15,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.08,
      size.height * 0.75,
      size.width * 0.1,
      size.height * 0.18,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 🏢 BUSINESS WATERCOLOR PAINTERS

/// Business background painter
class WatercolorBusinessBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Gradient pozadí
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color(0xFF006994),
        const Color(0xFF4CAF50),
        const Color(0xFF2E8B8B),
      ],
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    paint.shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // Watercolor efekty
    paint.shader = null;

    // Velké watercolor skvrny
    paint.color = Colors.white.withValues(alpha: 0.1);
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.3),
      size.width * 0.3,
      paint,
    );

    paint.color = Colors.white.withValues(alpha: 0.05);
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.7),
      size.width * 0.4,
      paint,
    );

    // Menší detaily
    paint.color = Colors.white.withValues(alpha: 0.08);
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.8),
      size.width * 0.15,
      paint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.2),
      size.width * 0.12,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Business card painter
class WatercolorBusinessCardPainter extends CustomPainter {
  final Color color;

  WatercolorBusinessCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Hlavní watercolor tvar
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.05,
      size.width * 0.4,
      size.height * 0.1,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.15,
      size.width * 0.95,
      size.height * 0.25,
    );
    path.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.6,
      size.width * 0.85,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.3,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.7,
      size.width * 0.05,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Další vrstva
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.15);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.08,
      size.width * 0.6,
      size.height * 0.12,
    );
    path2.quadraticBezierTo(
      size.width * 0.85,
      size.height * 0.18,
      size.width * 0.9,
      size.height * 0.4,
    );
    path2.quadraticBezierTo(
      size.width * 0.88,
      size.height * 0.75,
      size.width * 0.7,
      size.height * 0.88,
    );
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.92,
      size.width * 0.15,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.08,
      size.height * 0.5,
      size.width * 0.1,
      size.height * 0.15,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
