/// 📹 TRAFFIC CAMERA MODELS - Modely pro dopravní kamery

/// Dopravní kamera
class TrafficCamera {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final CameraType type;
  final String direction;
  final bool isActive;
  final bool hasLiveStream;
  final CameraQuality quality;
  final DateTime lastUpdated;
  final String? description;

  const TrafficCamera({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.type,
    required this.direction,
    required this.isActive,
    required this.hasLiveStream,
    required this.quality,
    required this.lastUpdated,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'type': type.name,
      'direction': direction,
      'isActive': isActive,
      'hasLiveStream': hasLiveStream,
      'quality': quality.name,
      'lastUpdated': lastUpdated.toIso8601String(),
      'description': description,
    };
  }

  factory TrafficCamera.fromJson(Map<String, dynamic> json) {
    return TrafficCamera(
      id: json['id'] as String,
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      type: CameraType.values.firstWhere((e) => e.name == json['type']),
      direction: json['direction'] as String,
      isActive: json['isActive'] as bool,
      hasLiveStream: json['hasLiveStream'] as bool,
      quality: CameraQuality.values.firstWhere((e) => e.name == json['quality']),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      description: json['description'] as String?,
    );
  }
}

/// Typ kamery
enum CameraType {
  traffic,    // Dopravní kamera
  highway,    // Dálniční kamera
  city,       // Městská kamera
  parking,    // Parkovací kamera
  speed,      // Rychlostní kamera
  toll,       // Mýtná kamera
}

/// Kvalita kamery
enum CameraQuality {
  sd,         // 480p
  hd,         // 720p
  fullHd,     // 1080p
  uhd,        // 4K
}

/// Parkovací kamera
class ParkingCamera {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String parkingLotId;
  final int totalSpots;
  final int occupiedSpots;
  final bool isActive;
  final DateTime lastUpdated;

  const ParkingCamera({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.parkingLotId,
    required this.totalSpots,
    required this.occupiedSpots,
    required this.isActive,
    required this.lastUpdated,
  });

  int get availableSpots => totalSpots - occupiedSpots;
  double get occupancyRate => totalSpots > 0 ? occupiedSpots / totalSpots : 0.0;
  
  bool get isAlmostFull => occupancyRate > 0.9;
  bool get isFull => occupiedSpots >= totalSpots;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'parkingLotId': parkingLotId,
      'totalSpots': totalSpots,
      'occupiedSpots': occupiedSpots,
      'isActive': isActive,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory ParkingCamera.fromJson(Map<String, dynamic> json) {
    return ParkingCamera(
      id: json['id'] as String,
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      parkingLotId: json['parkingLotId'] as String,
      totalSpots: json['totalSpots'] as int,
      occupiedSpots: json['occupiedSpots'] as int,
      isActive: json['isActive'] as bool,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// Dopravní stav
class TrafficStatus {
  final TrafficLevel level;
  final int averageSpeed; // km/h
  final int congestionPercentage;
  final List<TrafficIncident> incidents;
  final DateTime lastUpdated;
  final List<String> affectedRoutes;

  const TrafficStatus({
    required this.level,
    required this.averageSpeed,
    required this.congestionPercentage,
    required this.incidents,
    required this.lastUpdated,
    required this.affectedRoutes,
  });

  bool get hasIncidents => incidents.isNotEmpty;
  bool get isHeavyTraffic => level == TrafficLevel.heavy;
  
  String get statusDescription {
    switch (level) {
      case TrafficLevel.light:
        return 'Plynulá doprava';
      case TrafficLevel.moderate:
        return 'Mírné zpoždění';
      case TrafficLevel.heavy:
        return 'Hustá doprava';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'averageSpeed': averageSpeed,
      'congestionPercentage': congestionPercentage,
      'incidents': incidents.map((i) => i.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'affectedRoutes': affectedRoutes,
    };
  }

  factory TrafficStatus.fromJson(Map<String, dynamic> json) {
    return TrafficStatus(
      level: TrafficLevel.values.firstWhere((e) => e.name == json['level']),
      averageSpeed: json['averageSpeed'] as int,
      congestionPercentage: json['congestionPercentage'] as int,
      incidents: (json['incidents'] as List<dynamic>)
          .map((i) => TrafficIncident.fromJson(i as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      affectedRoutes: (json['affectedRoutes'] as List<dynamic>).cast<String>(),
    );
  }
}

/// Úroveň dopravy
enum TrafficLevel {
  light,      // Plynulá
  moderate,   // Mírná
  heavy,      // Hustá
}

/// Dopravní incident
class TrafficIncident {
  final String id;
  final IncidentType type;
  final String description;
  final double latitude;
  final double longitude;
  final IncidentSeverity severity;
  final DateTime reportedAt;
  final DateTime? resolvedAt;

  const TrafficIncident({
    required this.id,
    required this.type,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.severity,
    required this.reportedAt,
    this.resolvedAt,
  });

  bool get isResolved => resolvedAt != null;
  Duration get duration => (resolvedAt ?? DateTime.now()).difference(reportedAt);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'severity': severity.name,
      'reportedAt': reportedAt.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
    };
  }

  factory TrafficIncident.fromJson(Map<String, dynamic> json) {
    return TrafficIncident(
      id: json['id'] as String,
      type: IncidentType.values.firstWhere((e) => e.name == json['type']),
      description: json['description'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      severity: IncidentSeverity.values.firstWhere((e) => e.name == json['severity']),
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      resolvedAt: json['resolvedAt'] != null
          ? DateTime.parse(json['resolvedAt'] as String)
          : null,
    );
  }
}

/// Typ incidentu
enum IncidentType {
  accident,     // Nehoda
  construction, // Stavba
  roadwork,     // Oprava cesty
  weather,      // Počasí
  event,        // Událost
  breakdown,    // Porucha vozidla
}

/// Závažnost incidentu
enum IncidentSeverity {
  low,          // Nízká
  medium,       // Střední
  high,         // Vysoká
  critical,     // Kritická
}

/// Snímek z kamery
class CameraSnapshot {
  final String id;
  final String cameraId;
  final String imageUrl;
  final DateTime timestamp;
  final TrafficDensity trafficDensity;

  const CameraSnapshot({
    required this.id,
    required this.cameraId,
    required this.imageUrl,
    required this.timestamp,
    required this.trafficDensity,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cameraId': cameraId,
      'imageUrl': imageUrl,
      'timestamp': timestamp.toIso8601String(),
      'trafficDensity': trafficDensity.name,
    };
  }

  factory CameraSnapshot.fromJson(Map<String, dynamic> json) {
    return CameraSnapshot(
      id: json['id'] as String,
      cameraId: json['cameraId'] as String,
      imageUrl: json['imageUrl'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      trafficDensity: TrafficDensity.values.firstWhere(
        (e) => e.name == json['trafficDensity'],
      ),
    );
  }
}

/// Hustota dopravy
enum TrafficDensity {
  empty,        // Prázdná
  light,        // Řídká
  moderate,     // Střední
  heavy,        // Hustá
  jammed,       // Zácpa
}

/// Aktualizace kamery
class CameraUpdate {
  final String cameraId;
  final String imageUrl;
  final DateTime timestamp;
  final TrafficDensity trafficDensity;

  const CameraUpdate({
    required this.cameraId,
    required this.imageUrl,
    required this.timestamp,
    required this.trafficDensity,
  });

  Map<String, dynamic> toJson() {
    return {
      'cameraId': cameraId,
      'imageUrl': imageUrl,
      'timestamp': timestamp.toIso8601String(),
      'trafficDensity': trafficDensity.name,
    };
  }

  factory CameraUpdate.fromJson(Map<String, dynamic> json) {
    return CameraUpdate(
      cameraId: json['cameraId'] as String,
      imageUrl: json['imageUrl'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      trafficDensity: TrafficDensity.values.firstWhere(
        (e) => e.name == json['trafficDensity'],
      ),
    );
  }
}
