import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ai_orchestrator.dart';

/// Služba pro učení AI z uživatelských interakcí
class AILearningService {
  static final AILearningService _instance = AILearningService._internal();
  factory AILearningService() => _instance;
  AILearningService._internal();

  bool _isInitialized = false;
  final List<UserInteraction> _interactions = [];
  final Map<String, double> _userPreferences = {};
  final Map<String, int> _queryPatterns = {};
  final Map<String, double> _responseQuality = {};
  
  // Konfigurace učení
  static const int _maxInteractions = 1000;
  static const double _learningRate = 0.1;
  static const double _decayFactor = 0.95;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🧠 Inicializuji AI Learning Service...');
      
      await _loadLearningData();
      
      _isInitialized = true;
      debugPrint('✅ AI Learning Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci AI Learning: $e');
      _isInitialized = true;
    }
  }

  /// Zaznamenání uživatelské interakce
  Future<void> recordInteraction(UserInteraction interaction) async {
    try {
      _interactions.add(interaction);
      
      // Omezení počtu uložených interakcí
      if (_interactions.length > _maxInteractions) {
        _interactions.removeAt(0);
      }
      
      // Aktualizace vzorců dotazů
      await _updateQueryPatterns(interaction.query);
      
      // Aktualizace kvality odpovědí
      await _updateResponseQuality(interaction);
      
      // Uložení dat
      await _saveLearningData();
      
      debugPrint('📝 Zaznamenána interakce: ${interaction.query}');
    } catch (e) {
      debugPrint('❌ Chyba při zaznamenávání interakce: $e');
    }
  }

  /// Aktualizace uživatelských preferencí na základě feedbacku
  Future<LearningResult> updateUserPreferences(UserFeedback feedback) async {
    try {
      final oldPreferences = Map<String, double>.from(_userPreferences);
      
      // Aktualizace preferencí na základě feedbacku
      for (final entry in feedback.preferences.entries) {
        final key = entry.key;
        final value = entry.value;
        
        if (_userPreferences.containsKey(key)) {
          // Exponenciální klouzavý průměr
          _userPreferences[key] = _userPreferences[key]! * (1 - _learningRate) + 
                                  value * _learningRate;
        } else {
          _userPreferences[key] = value;
        }
      }
      
      // Výpočet zlepšení confidence
      final confidenceImprovement = _calculateConfidenceImprovement(
        oldPreferences,
        _userPreferences,
      );
      
      // Identifikace nových zájmů
      final newInterests = _identifyNewInterests(feedback);
      
      final result = LearningResult(
        userId: feedback.userId,
        updatedPreferences: _userPreferences,
        newInterests: newInterests,
        confidenceImprovement: confidenceImprovement,
        timestamp: DateTime.now(),
      );
      
      await _saveLearningData();
      
      debugPrint('🎯 Aktualizovány preference pro ${feedback.userId}');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při aktualizaci preferencí: $e');
      return LearningResult(
        userId: feedback.userId,
        confidenceImprovement: 0.0,
        timestamp: DateTime.now(),
      );
    }
  }

  /// Optimalizace znalostní báze na základě naučených vzorců
  Future<void> optimizeKnowledgeBase() async {
    try {
      debugPrint('🔧 Optimalizuji znalostní bázi...');
      
      // Analýza nejčastějších dotazů
      final topQueries = _getTopQueries();
      
      // Analýza nejúspěšnějších odpovědí
      final bestResponses = _getBestResponses();
      
      // Identifikace mezer ve znalostech
      final knowledgeGaps = _identifyKnowledgeGaps();
      
      debugPrint('📊 Top dotazy: ${topQueries.take(5).toList()}');
      debugPrint('⭐ Nejlepší odpovědi: ${bestResponses.length}');
      debugPrint('❓ Mezery ve znalostech: ${knowledgeGaps.length}');
      
      // Zde by byla implementace aktualizace znalostní báze
      
    } catch (e) {
      debugPrint('❌ Chyba při optimalizaci znalostní báze: $e');
    }
  }

  /// Predikce úspěšnosti odpovědi
  Future<double> predictResponseSuccess(String query, String response) async {
    try {
      // Analýza podobnosti s úspěšnými interakcemi
      double similarity = 0.0;
      int count = 0;
      
      for (final interaction in _interactions) {
        if (interaction.wasHelpful) {
          final querySimilarity = _calculateSimilarity(query, interaction.query);
          final responseSimilarity = _calculateSimilarity(response, interaction.response);
          
          similarity += (querySimilarity + responseSimilarity) / 2;
          count++;
        }
      }
      
      if (count == 0) return 0.5; // Neutrální predikce
      
      return (similarity / count).clamp(0.0, 1.0);
    } catch (e) {
      debugPrint('❌ Chyba při predikci úspěšnosti: $e');
      return 0.5;
    }
  }

  /// Získání personalizovaných doporučení na základě učení
  Future<List<String>> getPersonalizedSuggestions(String userId) async {
    try {
      final suggestions = <String>[];
      
      // Doporučení na základě preferencí
      final topPreferences = _userPreferences.entries
          .where((e) => e.value > 0.7)
          .map((e) => e.key)
          .take(5)
          .toList();
      
      for (final preference in topPreferences) {
        suggestions.add(_generateSuggestionForPreference(preference));
      }
      
      // Doporučení na základě vzorců dotazů
      final topPatterns = _queryPatterns.entries
          .where((e) => e.value > 3)
          .map((e) => e.key)
          .take(3)
          .toList();
      
      for (final pattern in topPatterns) {
        suggestions.add(_generateSuggestionForPattern(pattern));
      }
      
      return suggestions.take(8).toList();
    } catch (e) {
      debugPrint('❌ Chyba při generování návrhů: $e');
      return [];
    }
  }

  /// Analýza trendů v uživatelském chování
  Future<Map<String, dynamic>> analyzeBehaviorTrends() async {
    try {
      final trends = <String, dynamic>{};
      
      // Analýza časových vzorců
      trends['time_patterns'] = _analyzeTimePatterns();
      
      // Analýza typů dotazů
      trends['query_types'] = _analyzeQueryTypes();
      
      // Analýza úspěšnosti odpovědí
      trends['response_success'] = _analyzeResponseSuccess();
      
      // Analýza preferencí
      trends['preference_evolution'] = _analyzePreferenceEvolution();
      
      return trends;
    } catch (e) {
      debugPrint('❌ Chyba při analýze trendů: $e');
      return {};
    }
  }

  /// Aktualizace vzorců dotazů
  Future<void> _updateQueryPatterns(String query) async {
    final keywords = _extractKeywords(query);
    
    for (final keyword in keywords) {
      _queryPatterns[keyword] = (_queryPatterns[keyword] ?? 0) + 1;
    }
    
    // Aplikace decay faktoru na staré vzorce
    for (final key in _queryPatterns.keys.toList()) {
      _queryPatterns[key] = (_queryPatterns[key]! * _decayFactor).round();
      if (_queryPatterns[key]! <= 0) {
        _queryPatterns.remove(key);
      }
    }
  }

  /// Aktualizace kvality odpovědí
  Future<void> _updateResponseQuality(UserInteraction interaction) async {
    final responseHash = _hashResponse(interaction.response);
    
    if (_responseQuality.containsKey(responseHash)) {
      // Aktualizace existující kvality
      final currentQuality = _responseQuality[responseHash]!;
      final newQuality = interaction.wasHelpful ? 1.0 : 0.0;
      
      _responseQuality[responseHash] = currentQuality * (1 - _learningRate) + 
                                      newQuality * _learningRate;
    } else {
      // Nová odpověď
      _responseQuality[responseHash] = interaction.wasHelpful ? 1.0 : 0.0;
    }
  }

  /// Výpočet zlepšení confidence
  double _calculateConfidenceImprovement(
    Map<String, double> oldPrefs,
    Map<String, double> newPrefs,
  ) {
    double improvement = 0.0;
    int count = 0;
    
    for (final key in newPrefs.keys) {
      if (oldPrefs.containsKey(key)) {
        improvement += (newPrefs[key]! - oldPrefs[key]!).abs();
        count++;
      }
    }
    
    return count > 0 ? improvement / count : 0.0;
  }

  /// Identifikace nových zájmů
  List<String> _identifyNewInterests(UserFeedback feedback) {
    final newInterests = <String>[];
    
    for (final entry in feedback.preferences.entries) {
      if (entry.value > 0.8 && !_userPreferences.containsKey(entry.key)) {
        newInterests.add(entry.key);
      }
    }
    
    return newInterests;
  }

  /// Získání nejčastějších dotazů
  List<String> _getTopQueries() {
    final queryFreq = <String, int>{};
    
    for (final interaction in _interactions) {
      final query = interaction.query.toLowerCase();
      queryFreq[query] = (queryFreq[query] ?? 0) + 1;
    }
    
    final sorted = queryFreq.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sorted.map((e) => e.key).toList();
  }

  /// Získání nejlepších odpovědí
  List<String> _getBestResponses() {
    return _responseQuality.entries
        .where((e) => e.value > 0.8)
        .map((e) => e.key)
        .toList();
  }

  /// Identifikace mezer ve znalostech
  List<String> _identifyKnowledgeGaps() {
    final gaps = <String>[];
    
    for (final interaction in _interactions) {
      if (!interaction.wasHelpful && interaction.confidence < 0.5) {
        gaps.add(interaction.query);
      }
    }
    
    return gaps;
  }

  /// Výpočet podobnosti mezi texty
  double _calculateSimilarity(String text1, String text2) {
    final words1 = text1.toLowerCase().split(' ').toSet();
    final words2 = text2.toLowerCase().split(' ').toSet();
    
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;
    
    return union > 0 ? intersection / union : 0.0;
  }

  /// Extrakce klíčových slov
  List<String> _extractKeywords(String text) {
    final stopWords = {'a', 'an', 'the', 'is', 'are', 'was', 'were', 'in', 'on', 'at'};
    
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .split(' ')
        .where((word) => word.length > 2 && !stopWords.contains(word))
        .toList();
  }

  /// Hash odpovědi pro identifikaci
  String _hashResponse(String response) {
    return response.hashCode.toString();
  }

  /// Generování návrhu pro preferenci
  String _generateSuggestionForPreference(String preference) {
    return 'Na základě vašich preferencí doporučuji: $preference';
  }

  /// Generování návrhu pro vzorec
  String _generateSuggestionForPattern(String pattern) {
    return 'Často se ptáte na: $pattern';
  }

  /// Analýza časových vzorců
  Map<String, dynamic> _analyzeTimePatterns() {
    final hourCounts = <int, int>{};
    
    for (final interaction in _interactions) {
      final hour = interaction.timestamp.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }
    
    return {'hourly_distribution': hourCounts};
  }

  /// Analýza typů dotazů
  Map<String, dynamic> _analyzeQueryTypes() {
    final typeCounts = <String, int>{};
    
    for (final interaction in _interactions) {
      final type = _classifyQueryType(interaction.query);
      typeCounts[type] = (typeCounts[type] ?? 0) + 1;
    }
    
    return {'query_type_distribution': typeCounts};
  }

  /// Klasifikace typu dotazu
  String _classifyQueryType(String query) {
    final lowerQuery = query.toLowerCase();
    
    if (lowerQuery.contains('kde') || lowerQuery.contains('jak se dostanu')) {
      return 'navigation';
    } else if (lowerQuery.contains('doporuč') || lowerQuery.contains('najdi')) {
      return 'recommendation';
    } else if (lowerQuery.contains('co je') || lowerQuery.contains('kdo je')) {
      return 'factual';
    } else {
      return 'general';
    }
  }

  /// Analýza úspěšnosti odpovědí
  Map<String, dynamic> _analyzeResponseSuccess() {
    final successful = _interactions.where((i) => i.wasHelpful).length;
    final total = _interactions.length;
    
    return {
      'success_rate': total > 0 ? successful / total : 0.0,
      'total_interactions': total,
      'successful_interactions': successful,
    };
  }

  /// Analýza evoluce preferencí
  Map<String, dynamic> _analyzePreferenceEvolution() {
    // Implementace analýzy změn preferencí v čase
    return {'evolution_data': 'placeholder'};
  }

  /// Načtení dat učení
  Future<void> _loadLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Načtení interakcí
      final interactionsJson = prefs.getString('ai_interactions');
      if (interactionsJson != null) {
        final List<dynamic> data = jsonDecode(interactionsJson);
        _interactions.clear();
        _interactions.addAll(
          data.map((json) => UserInteraction.fromJson(json)).toList(),
        );
      }
      
      // Načtení preferencí
      final preferencesJson = prefs.getString('ai_preferences');
      if (preferencesJson != null) {
        final Map<String, dynamic> data = jsonDecode(preferencesJson);
        _userPreferences.clear();
        _userPreferences.addAll(
          data.map((key, value) => MapEntry(key, value.toDouble())),
        );
      }
      
      // Načtení vzorců
      final patternsJson = prefs.getString('ai_patterns');
      if (patternsJson != null) {
        final Map<String, dynamic> data = jsonDecode(patternsJson);
        _queryPatterns.clear();
        _queryPatterns.addAll(
          data.map((key, value) => MapEntry(key, value as int)),
        );
      }
      
    } catch (e) {
      debugPrint('❌ Chyba při načítání dat učení: $e');
    }
  }

  /// Uložení dat učení
  Future<void> _saveLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Uložení interakcí
      await prefs.setString(
        'ai_interactions',
        jsonEncode(_interactions.map((i) => i.toJson()).toList()),
      );
      
      // Uložení preferencí
      await prefs.setString(
        'ai_preferences',
        jsonEncode(_userPreferences),
      );
      
      // Uložení vzorců
      await prefs.setString(
        'ai_patterns',
        jsonEncode(_queryPatterns),
      );
      
    } catch (e) {
      debugPrint('❌ Chyba při ukládání dat učení: $e');
    }
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  int get interactionCount => _interactions.length;
  Map<String, double> get userPreferences => Map.unmodifiable(_userPreferences);
  Map<String, int> get queryPatterns => Map.unmodifiable(_queryPatterns);
}

/// Uživatelský feedback
class UserFeedback {
  final String userId;
  final Map<String, double> preferences;
  final List<String> interests;
  final DateTime timestamp;

  const UserFeedback({
    required this.userId,
    required this.preferences,
    this.interests = const [],
    required this.timestamp,
  });
}
