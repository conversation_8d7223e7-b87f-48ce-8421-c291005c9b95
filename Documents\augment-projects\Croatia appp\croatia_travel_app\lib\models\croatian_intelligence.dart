/// 🇭🇷 CROATIAN INTELLIGENCE MODELS - Modely pro chorvatské lokální informace

/// Chorvatská událost
class CroatianEvent {
  final String id;
  final String name;
  final String description;
  final String location;
  final EventCategory category;
  final DateTime startDate;
  final DateTime endDate;
  final double? price;
  final String? website;
  final String? imageUrl;
  final bool isRecurring;
  final List<String> tags;

  const CroatianEvent({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.category,
    required this.startDate,
    required this.endDate,
    this.price,
    this.website,
    this.imageUrl,
    this.isRecurring = false,
    this.tags = const [],
  });

  bool get isActive => DateTime.now().isBefore(endDate);
  bool get isUpcoming => DateTime.now().isBefore(startDate);
  bool get isOngoing => DateTime.now().isAfter(startDate) && DateTime.now().isBefore(endDate);
  
  Duration get duration => endDate.difference(startDate);
  int get daysUntilStart => startDate.difference(DateTime.now()).inDays;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'category': category.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'price': price,
      'website': website,
      'imageUrl': imageUrl,
      'isRecurring': isRecurring,
      'tags': tags,
    };
  }

  factory CroatianEvent.fromJson(Map<String, dynamic> json) {
    return CroatianEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      category: EventCategory.values.firstWhere((c) => c.name == json['category']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      price: json['price'] as double?,
      website: json['website'] as String?,
      imageUrl: json['imageUrl'] as String?,
      isRecurring: json['isRecurring'] as bool? ?? false,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

/// Kategorie události
enum EventCategory {
  culture,      // Kultura
  music,        // Hudba
  food,         // Jídlo a pití
  sports,       // Sport
  festival,     // Festival
  exhibition,   // Výstava
  theater,      // Divadlo
  dance,        // Tanec
  traditional,  // Tradiční
  modern,       // Moderní
}

extension EventCategoryExtension on EventCategory {
  String get displayName {
    switch (this) {
      case EventCategory.culture:
        return 'Kultura';
      case EventCategory.music:
        return 'Hudba';
      case EventCategory.food:
        return 'Jídlo a pití';
      case EventCategory.sports:
        return 'Sport';
      case EventCategory.festival:
        return 'Festival';
      case EventCategory.exhibition:
        return 'Výstava';
      case EventCategory.theater:
        return 'Divadlo';
      case EventCategory.dance:
        return 'Tanec';
      case EventCategory.traditional:
        return 'Tradiční';
      case EventCategory.modern:
        return 'Moderní';
    }
  }

  String get icon {
    switch (this) {
      case EventCategory.culture:
        return '🏛️';
      case EventCategory.music:
        return '🎵';
      case EventCategory.food:
        return '🍷';
      case EventCategory.sports:
        return '⚽';
      case EventCategory.festival:
        return '🎪';
      case EventCategory.exhibition:
        return '🖼️';
      case EventCategory.theater:
        return '🎭';
      case EventCategory.dance:
        return '💃';
      case EventCategory.traditional:
        return '🏺';
      case EventCategory.modern:
        return '🎨';
    }
  }
}

/// Trajektový spoj
class FerrySchedule {
  final String id;
  final FerryRoute route;
  final List<TimeOfDayModel> departures;
  final Duration duration;
  final double price;
  final String operator;
  final String vesselName;
  final List<int> operatingDays; // 1-7 (pondělí-neděle)
  final String? notes;

  const FerrySchedule({
    required this.id,
    required this.route,
    required this.departures,
    required this.duration,
    required this.price,
    required this.operator,
    required this.vesselName,
    required this.operatingDays,
    this.notes,
  });

  bool isOperatingToday() {
    final today = DateTime.now().weekday;
    return operatingDays.contains(today);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'route': route.toJson(),
      'departures': departures.map((d) => d.toJson()).toList(),
      'duration': duration.inMinutes,
      'price': price,
      'operator': operator,
      'vesselName': vesselName,
      'operatingDays': operatingDays,
      'notes': notes,
    };
  }
}

/// Trajektová trasa
class FerryRoute {
  final String from;
  final String to;
  final double distance; // v km

  const FerryRoute({
    required this.from,
    required this.to,
    required this.distance,
  });

  String get routeName => '$from - $to';

  Map<String, dynamic> toJson() {
    return {
      'from': from,
      'to': to,
      'distance': distance,
    };
  }
}

/// Čas dne model
class TimeOfDayModel {
  final int hour;
  final int minute;

  const TimeOfDayModel({
    required this.hour,
    required this.minute,
  });

  String get formatted => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

  Map<String, dynamic> toJson() {
    return {
      'hour': hour,
      'minute': minute,
    };
  }
}

/// Trajektové spojení
class FerryConnection {
  final String id;
  final FerryRoute route;
  final DateTime departure;
  final DateTime arrival;
  final double price;
  final String operator;
  final String vesselName;
  final int availableSeats;
  final WeatherCondition weatherConditions;

  const FerryConnection({
    required this.id,
    required this.route,
    required this.departure,
    required this.arrival,
    required this.price,
    required this.operator,
    required this.vesselName,
    required this.availableSeats,
    required this.weatherConditions,
  });

  Duration get travelTime => arrival.difference(departure);
  bool get isAvailable => availableSeats > 0;
  bool get isDeparted => DateTime.now().isAfter(departure);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'route': route.toJson(),
      'departure': departure.toIso8601String(),
      'arrival': arrival.toIso8601String(),
      'price': price,
      'operator': operator,
      'vesselName': vesselName,
      'availableSeats': availableSeats,
      'weatherConditions': weatherConditions.name,
    };
  }
}

/// Vinařský region
class WineRegion {
  final String id;
  final String name;
  final String description;
  final double latitude;
  final double longitude;
  final List<String> specialties;
  final String bestVisitingTime;
  final List<Winery> wineries;
  final String? imageUrl;

  const WineRegion({
    required this.id,
    required this.name,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.specialties,
    required this.bestVisitingTime,
    this.wineries = const [],
    this.imageUrl,
  });

  int get wineryCount => wineries.length;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'specialties': specialties,
      'bestVisitingTime': bestVisitingTime,
      'wineries': wineries.map((w) => w.toJson()).toList(),
      'imageUrl': imageUrl,
    };
  }
}

/// Vinařství
class Winery {
  final String name;
  final String location;
  final List<String> specialWines;
  final String visitingHours;
  final String contactInfo;
  final double? rating;

  const Winery({
    required this.name,
    required this.location,
    required this.specialWines,
    required this.visitingHours,
    required this.contactInfo,
    this.rating,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'location': location,
      'specialWines': specialWines,
      'visitingHours': visitingHours,
      'contactInfo': contactInfo,
      'rating': rating,
    };
  }
}

/// Historický fakt
class HistoricalFact {
  final String id;
  final String title;
  final String description;
  final String period;
  final double? latitude;
  final double? longitude;
  final HistoricalCategory category;
  final String? funFact;
  final String? imageUrl;

  const HistoricalFact({
    required this.id,
    required this.title,
    required this.description,
    required this.period,
    this.latitude,
    this.longitude,
    required this.category,
    this.funFact,
    this.imageUrl,
  });

  bool get hasLocation => latitude != null && longitude != null;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'period': period,
      'latitude': latitude,
      'longitude': longitude,
      'category': category.name,
      'funFact': funFact,
      'imageUrl': imageUrl,
    };
  }
}

/// Kategorie historických faktů
enum HistoricalCategory {
  architecture,  // Architektura
  culture,       // Kultura
  war,          // Válka
  religion,     // Náboženství
  trade,        // Obchod
  politics,     // Politika
}

/// Chorvatská fráze
class CroatianPhrase {
  final String id;
  final String croatian;
  final String pronunciation;
  final String english;
  final String czech;
  final PhraseCategory category;
  final DifficultyLevel difficulty;
  final String usage;
  final String? audioUrl;

  const CroatianPhrase({
    required this.id,
    required this.croatian,
    required this.pronunciation,
    required this.english,
    required this.czech,
    required this.category,
    required this.difficulty,
    required this.usage,
    this.audioUrl,
  });

  bool get hasAudio => audioUrl != null;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'croatian': croatian,
      'pronunciation': pronunciation,
      'english': english,
      'czech': czech,
      'category': category.name,
      'difficulty': difficulty.name,
      'usage': usage,
      'audioUrl': audioUrl,
    };
  }
}

/// Kategorie frází
enum PhraseCategory {
  greetings,    // Pozdravy
  courtesy,     // Zdvořilost
  directions,   // Směry
  food,         // Jídlo
  shopping,     // Nakupování
  emergency,    // Nouzové
  numbers,      // Čísla
  time,         // Čas
}

/// Úroveň obtížnosti
enum DifficultyLevel {
  easy,         // Snadné
  medium,       // Střední
  hard,         // Těžké
}

/// Počasí
enum WeatherCondition {
  sunny,        // Slunečno
  cloudy,       // Oblačno
  rainy,        // Deštivo
  stormy,       // Bouřka
  windy,        // Větrno
  foggy,        // Mlhavo
  snowy,        // Sněžení
}

/// Doporučení podle počasí
class WeatherRecommendation {
  final String id;
  final String title;
  final String description;
  final String activity;
  final String icon;
  final RecommendationPriority priority;

  const WeatherRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.activity,
    required this.icon,
    required this.priority,
  });
}

/// Priorita doporučení
enum RecommendationPriority {
  low,
  medium,
  high,
}

/// Sezónní doporučení
class SeasonalRecommendation {
  final String id;
  final String title;
  final String description;
  final Season season;
  final List<String> activities;
  final List<String> bestLocations;
  final List<String> tips;

  const SeasonalRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.season,
    required this.activities,
    required this.bestLocations,
    required this.tips,
  });
}

/// Sezóna
enum Season {
  spring,   // Jaro
  summer,   // Léto
  autumn,   // Podzim
  winter,   // Zima
}

/// Tradiční jídlo
class TraditionalFood {
  final String name;
  final String description;
  final String region;
  final List<String> ingredients;
  final String preparationTime;
  final DifficultyLevel difficulty;
  final String story;
  final String? imageUrl;

  const TraditionalFood({
    required this.name,
    required this.description,
    required this.region,
    required this.ingredients,
    required this.preparationTime,
    required this.difficulty,
    required this.story,
    this.imageUrl,
  });
}

/// Weather data
class WeatherData {
  final double temperature;
  final WeatherCondition condition;
  final int humidity;
  final double windSpeed;
  final DateTime timestamp;

  const WeatherData({
    required this.temperature,
    required this.condition,
    required this.humidity,
    required this.windSpeed,
    required this.timestamp,
  });
}
