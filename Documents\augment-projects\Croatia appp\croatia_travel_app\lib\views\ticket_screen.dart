import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';

class TicketScreen extends StatefulWidget {
  const TicketScreen({super.key});

  @override
  State<TicketScreen> createState() => _TicketScreenState();
}

class _TicketScreenState extends State<TicketScreen>
    with TickerProviderStateMixin {
  final TicketService _ticketService = TicketService();
  final TextEditingController _searchController = TextEditingController();

  List<Ticket> _tickets = [];
  List<Ticket> _filteredTickets = [];
  bool _isLoading = true;

  // Filtry
  TicketType? _selectedType;
  String? _selectedRegion;
  double _maxPrice = 100.0;
  bool _showGroupDiscounts = false;
  bool _showSeasonalDiscounts = false;

  late AnimationController _filterController;
  late AnimationController _cardController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadTickets();
  }

  void _initializeAnimations() {
    _filterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _cardController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  Future<void> _loadTickets() async {
    setState(() => _isLoading = true);

    try {
      await _ticketService.initialize();
      _tickets = _ticketService.tickets;
      _filteredTickets = _tickets;

      _cardController.forward();
    } catch (e) {
      debugPrint('Chyba při načítání vstupenek: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _filterTickets() {
    setState(() {
      _filteredTickets = _ticketService.searchTickets(
        query: _searchController.text,
        type: _selectedType,
        region: _selectedRegion,
        maxPrice: _maxPrice,
        hasGroupDiscounts: _showGroupDiscounts ? true : null,
        hasSeasonalDiscounts: _showSeasonalDiscounts ? true : null,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(
          'Vstupenky',
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF2C2C2C),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showFilterDialog(),
            icon: const Icon(Icons.tune, color: Color(0xFF006994)),
          ),
        ],
      ),
      body: CustomPaint(
        painter: WatercolorTicketBackgroundPainter(),
        child: Column(
          children: [
            // Vyhledávání
            _buildSearchBar(),

            // Rychlé filtry
            _buildQuickFilters(),

            // Seznam vstupenek
            Expanded(
              child: _isLoading ? _buildLoadingState() : _buildTicketsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Hledat vstupenky...',
          hintStyle: GoogleFonts.inter(color: const Color(0xFF999999)),
          prefixIcon: const Icon(Icons.search, color: Color(0xFF006994)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        onChanged: (_) => _filterTickets(),
      ),
    );
  }

  Widget _buildQuickFilters() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            'Muzea',
            _selectedType == TicketType.museum,
            () => _toggleTypeFilter(TicketType.museum),
            Icons.museum,
          ),
          _buildFilterChip(
            'Památky',
            _selectedType == TicketType.monument,
            () => _toggleTypeFilter(TicketType.monument),
            Icons.account_balance,
          ),
          _buildFilterChip(
            'Parky',
            _selectedType == TicketType.park,
            () => _toggleTypeFilter(TicketType.park),
            Icons.park,
          ),
          _buildFilterChip(
            'Skupinové slevy',
            _showGroupDiscounts,
            () => _toggleGroupDiscounts(),
            Icons.group,
          ),
          _buildFilterChip(
            'Sezónní slevy',
            _showSeasonalDiscounts,
            () => _toggleSeasonalDiscounts(),
            Icons.local_offer,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : const Color(0xFF006994),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: isSelected ? Colors.white : const Color(0xFF006994),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: Colors.white,
        selectedColor: const Color(0xFF006994),
        checkmarkColor: Colors.white,
        elevation: 2,
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF006994)),
      ),
    );
  }

  Widget _buildTicketsList() {
    if (_filteredTickets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.confirmation_number_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Žádné vstupenky nenalezeny',
              style: GoogleFonts.inter(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Zkuste změnit filtry nebo vyhledávání',
              style: GoogleFonts.inter(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredTickets.length,
          itemBuilder: (context, index) {
            final ticket = _filteredTickets[index];
            final delay = index * 0.1;
            final animation = Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: _cardController,
                curve: Interval(delay, 1.0, curve: Curves.easeOut),
              ),
            );

            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.3),
                  end: Offset.zero,
                ).animate(animation),
                child: _buildTicketCard(ticket),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTicketCard(Ticket ticket) {
    final adultPrice =
        ticket.pricing.categories
            .where((cat) => cat.category == TicketCategory.adult)
            .firstOrNull
            ?.price ??
        0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header s obrázkem
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                colors: [
                  _getTypeColor(ticket.type).withOpacity(0.8),
                  _getTypeColor(ticket.type),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                // Pozadí pattern
                Positioned.fill(
                  child: CustomPaint(painter: TicketPatternPainter()),
                ),

                // Obsah
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ticket.title,
                              style: GoogleFonts.inter(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              ticket.location,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                            const Spacer(),
                            Row(
                              children: [
                                Icon(
                                  _getTypeIcon(ticket.type),
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getTypeLabel(ticket.type),
                                  style: GoogleFonts.inter(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Cena
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (adultPrice > 0) ...[
                            Text(
                              '${adultPrice.toStringAsFixed(0)} €',
                              style: GoogleFonts.inter(
                                fontSize: 24,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              'od osoby',
                              style: GoogleFonts.inter(
                                fontSize: 10,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ] else ...[
                            Text(
                              'ZDARMA',
                              style: GoogleFonts.inter(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                          ],

                          const Spacer(),

                          // Oblíbené
                          GestureDetector(
                            onTap: () => _toggleFavorite(ticket),
                            child: Icon(
                              _ticketService.isFavorite(ticket.id)
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Obsah
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ticket.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Tagy a funkce
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: [
                    if (ticket.hasGroupDiscounts)
                      _buildFeatureTag(
                        'Skupinové slevy',
                        Icons.group,
                        Colors.green,
                      ),
                    if (ticket.hasSeasonalDiscounts)
                      _buildFeatureTag(
                        'Sezónní slevy',
                        Icons.local_offer,
                        Colors.orange,
                      ),
                    if (ticket.features.hasQRCode)
                      _buildFeatureTag('QR kód', Icons.qr_code, Colors.blue),
                    if (ticket.features.isSkipTheLine)
                      _buildFeatureTag(
                        'Bez fronty',
                        Icons.fast_forward,
                        Colors.purple,
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // Tlačítka
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _showTicketDetails(ticket),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFF006994)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Detaily',
                          style: GoogleFonts.inter(
                            color: const Color(0xFF006994),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () => _ticketService.openBookingPage(ticket),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF006994),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Rezervovat',
                          style: GoogleFonts.inter(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureTag(String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(TicketType type) {
    switch (type) {
      case TicketType.museum:
        return const Color(0xFF8E24AA);
      case TicketType.monument:
        return const Color(0xFF006994);
      case TicketType.park:
        return const Color(0xFF4CAF50);
      case TicketType.event:
        return const Color(0xFFFF6B35);
      case TicketType.tour:
        return const Color(0xFF2196F3);
      case TicketType.activity:
        return const Color(0xFFFF9800);
      case TicketType.transport:
        return const Color(0xFF607D8B);
      case TicketType.combo:
        return const Color(0xFFE91E63);
    }
  }

  IconData _getTypeIcon(TicketType type) {
    switch (type) {
      case TicketType.museum:
        return Icons.museum;
      case TicketType.monument:
        return Icons.account_balance;
      case TicketType.park:
        return Icons.park;
      case TicketType.event:
        return Icons.event;
      case TicketType.tour:
        return Icons.tour;
      case TicketType.activity:
        return Icons.local_activity;
      case TicketType.transport:
        return Icons.directions_bus;
      case TicketType.combo:
        return Icons.card_giftcard;
    }
  }

  String _getTypeLabel(TicketType type) {
    switch (type) {
      case TicketType.museum:
        return 'Muzeum';
      case TicketType.monument:
        return 'Památka';
      case TicketType.park:
        return 'Park';
      case TicketType.event:
        return 'Akce';
      case TicketType.tour:
        return 'Prohlídka';
      case TicketType.activity:
        return 'Aktivita';
      case TicketType.transport:
        return 'Doprava';
      case TicketType.combo:
        return 'Kombinace';
    }
  }

  void _toggleTypeFilter(TicketType type) {
    setState(() {
      _selectedType = _selectedType == type ? null : type;
      _filterTickets();
    });
  }

  void _toggleGroupDiscounts() {
    setState(() {
      _showGroupDiscounts = !_showGroupDiscounts;
      _filterTickets();
    });
  }

  void _toggleSeasonalDiscounts() {
    setState(() {
      _showSeasonalDiscounts = !_showSeasonalDiscounts;
      _filterTickets();
    });
  }

  void _toggleFavorite(Ticket ticket) {
    setState(() {
      if (_ticketService.isFavorite(ticket.id)) {
        _ticketService.removeFromFavorites(ticket.id);
      } else {
        _ticketService.addToFavorites(ticket);
      }
    });
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Filtry',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Expanded(
              child: Center(
                child: Text('Pokročilé filtry budou implementovány'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showTicketDetails(Ticket ticket) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ticket.title,
                      style: GoogleFonts.inter(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      ticket.description,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Ceny
                    Text(
                      'Ceny',
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...ticket.pricing.categories.map(
                      (category) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              category.description,
                              style: GoogleFonts.inter(fontSize: 14),
                            ),
                            Text(
                              category.price > 0
                                  ? '${category.price.toStringAsFixed(0)} €'
                                  : 'Zdarma',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Kontakt
                    Text(
                      'Kontakt',
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      ticket.provider.name,
                      style: GoogleFonts.inter(fontSize: 14),
                    ),
                    if (ticket.provider.phoneNumber != null)
                      Text(
                        ticket.provider.phoneNumber!,
                        style: GoogleFonts.inter(fontSize: 14),
                      ),

                    const SizedBox(height: 30),

                    // Rezervovat tlačítko
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _ticketService.openBookingPage(ticket);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF006994),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Rezervovat na oficiálních stránkách',
                          style: GoogleFonts.inter(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterController.dispose();
    _cardController.dispose();
    super.dispose();
  }
}

// Watercolor painter pro pozadí
class WatercolorTicketBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt
    final path = Path();
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path.lineTo(size.width * 0.95, size.height * 0.9);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.95,
      size.width * 0.1,
      size.height * 0.85,
    );
    path.close();

    paint.color = const Color(0xFF006994).withOpacity(0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Pattern painter pro karty vstupenek
class TicketPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Geometrický pattern
    for (int i = 0; i < 10; i++) {
      final x = (size.width / 10) * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x + size.width * 0.2, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
